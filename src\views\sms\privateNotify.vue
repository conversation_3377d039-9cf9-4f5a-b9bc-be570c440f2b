<template>
  <div class="sms-list-container">


    <!-- 短信列表容器 -->
    <div class="sms-list-wrapper">
      <!-- 顶部导航栏 -->
      <div class="top-nav">
        <div class="nav-left">
          <button class="nav-button glass-button" @click="goBack">
            <svg fill="none" height="20" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" width="20">
              <path d="M19 12H5M12 19l-7-7 7-7" />
            </svg>
          </button>
        </div>
        <div class="nav-center">
          <h1 class="nav-title">
            短信验证码
          </h1>
        </div>
        <div class="nav-right">
          <button class="nav-button glass-button" @click="refreshMessages">
            <svg fill="none" height="20" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" width="20">
              <path d="M1 4v6h6M23 20v-6h-6" />
              <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" />
            </svg>
          </button>
        </div>
      </div>
      <div ref="listContainer" class="sms-list">
      <div
        v-for="(message, index) in messages"
        :key="message.id || index"
        :class="{ 'fade-in': message.animated }"
        class="sms-item glass-panel"
        @animationend="message.animated = false"
      >
        <!-- 短信头部信息 -->
        <div class="sms-header">
          <div v-if="getPhoneFromParsed(parseMessageContent(message.content))" class="sms-phone">
            <div class="phone-icon">
              <svg fill="none" height="14" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" width="14">
                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
              </svg>
            </div>
            {{ getPhoneFromParsed(parseMessageContent(message.content)) }}
          </div>
          <div v-else class="sms-sender">
            <div class="sender-icon">
              <svg fill="none" height="14" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" width="14">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
              </svg>
            </div>
            短信通知
          </div>
          <div class="sms-time">
            <div class="time-icon">
              <svg fill="none" height="12" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" width="12">
                <circle cx="12" cy="12" r="10"/>
                <polyline points="12,6 12,12 16,14"/>
              </svg>
            </div>
            {{ formatRelativeTime(getTimeFromParsed(parseMessageContent(message.content))) }}
          </div>
        </div>

        <!-- 验证码卡片或普通短信内容 -->
        <div v-if="getVerificationCodeFromParsed(parseMessageContent(message.content))" class="verification-section">
          <div class="verification-code-display">
            <span class="code-label">验证码</span>
            <span class="code-value">{{ getVerificationCodeFromParsed(parseMessageContent(message.content)) }}</span>
          </div>
          <!-- 验证码卡片内的短信内容 -->
          <div class="verification-message-content">
            <div v-for="(line, lineIndex) in parseMessageContent(message.content)" :key="lineIndex" class="sms-line">
              <div v-if="line.type === 'message'" class="verification-sms-text">
                {{ line.content }}
              </div>
            </div>
          </div>
          <button
            :class="{ copied: copiedStates[index] }"
            class="copy-code-button"
            @click="copyVerificationCodeFromParsed(parseMessageContent(message.content), index)"
          >
            <span class="button-icon">
              <svg v-if="!copiedStates[index]" fill="none" height="16" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" width="16">
                <rect height="13" rx="2" ry="2" width="13" x="9" y="9" />
                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" />
              </svg>
              <svg v-else fill="none" height="16" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" width="16">
                <path d="M20 6L9 17l-5-5" />
              </svg>
            </span>
            <span class="button-text">
              {{ copiedStates[index] ? '已复制' : '复制验证码' }}
            </span>
          </button>
        </div>

        <!-- 普通短信内容（无验证码时显示） -->
        <div v-else class="sms-content">
          <div v-for="(line, lineIndex) in parseMessageContent(message.content)" :key="lineIndex" class="sms-line">
            <div v-if="line.type === 'message'" class="sms-text">
              {{ line.content }}
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="messages.length === 0 && !loading" class="empty-state glass-panel">
        <div class="empty-icon">
          <svg fill="none" height="64" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24" width="64">
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
          </svg>
        </div>
        <h3 class="empty-title">暂无短信</h3>
        <p class="empty-description">
          等待接收新的短信验证码
        </p>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state glass-panel">
        <div class="loading-spinner gradient-spinner" />
        <p class="loading-text">
          加载中...
        </p>
      </div>

      <!-- 加载更多状态 -->
      <div v-if="loadingMore && !loading" class="loading-more-state">
        <div class="loading-more-spinner gradient-spinner" />
        <p class="loading-more-text">
          加载更多...
        </p>
      </div>

      <!-- 没有更多数据提示 -->
      <div v-if="!hasMore && messages.length > 0 && !loading && !loadingMore" class="no-more-state">
        <div class="no-more-divider"></div>
        <p class="no-more-text">
          已加载全部内容
        </p>
      </div>
    </div>
    </div>

    <!-- Toast 提示 -->
    <div v-if="showToast" :class="{ show: showToast }" class="toast glass-panel">
      <div class="toast-icon">
        <svg fill="none" height="16" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" width="16">
          <path d="M20 6L9 17l-5-5" />
        </svg>
      </div>
      <span>验证码已复制到剪贴板</span>
    </div>
  </div>
</template>

<script setup>
import {useAppStore, useAuthStore} from '@/store'
import api from '@/views/sms/sms.js'
import { computed, nextTick, onMounted, onUnmounted, ref } from 'vue'
import { WebSocketManager } from './WebSocketManager'

// 响应式数据
const messages = ref([])
const copiedStates = ref({})
const loading = ref(false)
const showToast = ref(false)
const listContainer = ref(null)
const loadingMore = ref(false)
const hasMore = ref(true)
const totalPages = ref(0)
const originalBodyStyle = ref(null)
const { accessToken } = useAuthStore()

// 时间刷新相关
const timeRefreshInterval = ref(null)
const currentTime = ref(new Date())

// 主题相关
const appStore = useAppStore()
computed(() => {
  const primaryColor = appStore.primaryColor
  return {
    primary: primaryColor,
    primaryRgb: primaryColor.replace('#', '').match(/.{2}/g)?.map(hex => Number.parseInt(hex, 16)).join(', ') || '0, 172, 193',
  }
})
let wsManager = null

/**
 * 从短信内容中提取验证码
 */
function getVerificationCode(content) {
  if (!content)
    return null

  // 常见验证码匹配模式
  const patterns = [
    /验证码[为是:：]\s*([A-Z0-9]{4,8})/i,
    /验证码为[:：]?\s*([A-Z0-9]{4,8})/i,
    /验证码是[:：]?\s*([A-Z0-9]{4,8})/i,
    /动态码[为是:：]\s*([A-Z0-9]{4,8})/i,
    /code[:\s]+([A-Z0-9]{4,8})/i,
    /：\s*([A-Z0-9]{4,8})/i,
  ]

  for (const pattern of patterns) {
    const match = content.match(pattern)
    if (match && match[1]) {
      return match[1].toUpperCase()
    }
  }

  // 查找独立的数字验证码
  const lines = content.split('\n')
  for (const line of lines) {
    if (line.includes('验证码') || line.includes('动态码')) {
      const codeMatch = line.match(/(\d{4,6})/)
      if (codeMatch) {
        return codeMatch[1]
      }
    }
  }

  return null
}

/**
 * 从解析后的内容中提取验证码
 */
function getVerificationCodeFromParsed(parsedContent) {
  if (!parsedContent || !Array.isArray(parsedContent))
    return null

  // 在消息内容中查找验证码
  for (const line of parsedContent) {
    if (line.type === 'message') {
      const code = getVerificationCode(line.content)
      if (code)
        return code
    }
  }

  return null
}

/**
 * 从解析后的内容中提取手机号
 */
function getPhoneFromParsed(parsedContent) {
  if (!parsedContent || !Array.isArray(parsedContent))
    return null

  for (const line of parsedContent) {
    if (line.type === 'phone') {
      return line.content
    }
  }

  return null
}

/**
 * 从解析后的内容中提取时间
 */
function getTimeFromParsed(parsedContent) {
  if (!parsedContent || !Array.isArray(parsedContent))
    return null

  for (const line of parsedContent) {
    if (line.type === 'time') {
      return line.content
    }
  }

  return null
}

/**
 * 格式化相对时间
 */
function formatRelativeTime(timeString) {
  if (!timeString) {
    return '刚刚'
  }

  try {
    const messageTime = new Date(timeString)
    // 使用响应式的当前时间，这样当 currentTime 更新时，显示会自动刷新
    const now = currentTime.value
    const diffMs = now.getTime() - messageTime.getTime()
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMinutes < 1) {
      return '刚刚'
    } else if (diffMinutes < 3) {
      return '1分钟前'
    } else if (diffMinutes < 5) {
      return '3分钟前'
    } else if (diffMinutes < 15) {
      return '10分钟前'
    } else if (diffMinutes < 45) {
      return '半小时前'
    } else if (diffHours < 2) {
      return '1小时前'
    } else if (diffHours < 24) {
      return `${diffHours}小时前`
    } else if (diffDays < 7) {
      return `${diffDays}天前`
    } else {
      // 超过一周显示具体日期
      return messageTime.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric'
      })
    }
  } catch (error) {
    return '刚刚'
  }
}

/**
 * 启动时间刷新定时器
 */
function startTimeRefresh() {
  // 清除现有定时器
  if (timeRefreshInterval.value) {
    clearInterval(timeRefreshInterval.value)
  }

  // 每分钟更新一次当前时间
  timeRefreshInterval.value = setInterval(() => {
    currentTime.value = new Date()
  }, 60000) // 60秒 = 1分钟
}

/**
 * 停止时间刷新定时器
 */
function stopTimeRefresh() {
  if (timeRefreshInterval.value) {
    clearInterval(timeRefreshInterval.value)
    timeRefreshInterval.value = null
  }
}

/**
 * 复制验证码到剪贴板（从解析后的内容）
 */
async function copyVerificationCodeFromParsed(parsedContent, index) {
  const code = getVerificationCodeFromParsed(parsedContent)
  if (!code)
    return

  try {
    await navigator.clipboard.writeText(code)

    // 设置复制状态
    copiedStates.value[index] = true

    // 显示 Toast
    showToast.value = true

    // 重置状态
    setTimeout(() => {
      copiedStates.value[index] = false
    }, 2000)

    setTimeout(() => {
      showToast.value = false
    }, 3000)
  }
  catch (error) {
    console.error('复制失败:', error)
  }
}
/**
 * 解析短信内容，按行分类
 */
function parseMessageContent(content) {
  if (!content)
    return []

  const lines = content.split('\n').filter(line => line.trim())
  const result = []

  lines.forEach((line) => {
    const trimmedLine = line.trim()

    // 判断是否为手机号（纯数字，10-11位）
    if (/^\d{10,11}$/.test(trimmedLine)) {
      result.push({
        type: 'phone',
        content: trimmedLine,
      })
    }
    // 判断是否为时间格式
    else if (/^\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}$/.test(trimmedLine)) {
      result.push({
        type: 'time',
        content: trimmedLine,
      })
    }
    // 其他内容作为消息内容
    else {
      result.push({
        type: 'message',
        content: trimmedLine,
      })
    }
  })

  return result
}
/**
 * 返回上一页
 */
function goBack() {
  window.history.back()
}

/**
 * 获取短信列表
 */
const pageNumber = ref(1)
const pageSize = ref(10)
const smsType = ref('2') // 默认私有
async function fetchMessages(isLoadMore = false) {
  if (isLoadMore) {
    loadingMore.value = true
  }
  else {
    loading.value = true
  }

  const params = {
    smsType: smsType.value,
    pageNumber: pageNumber.value,
    pageSize: pageSize.value,
  }
  try {
    const response = await api.getPrivateNotify(params)

    if (response && response.data && Array.isArray(response.data.records)) {
      const newMessages = response.data.records
        .filter(msg => msg && typeof msg.content === 'string')
        .map(msg => ({
          ...msg,
          animated: !isLoadMore, // 只有首次加载才有动画
          timestamp: msg.timestamp || Date.now(),
        }))

      if (isLoadMore) {
        // 加载更多时追加到现有消息
        messages.value = [...messages.value, ...newMessages]
      }
      else {
        // 首次加载或刷新时替换所有消息
        messages.value = newMessages
      }

      // 更新分页信息
      totalPages.value = response.data.totalPage || 1
      hasMore.value = pageNumber.value < totalPages.value
    }
  }
  catch (error) {
    console.error('获取短信失败:', error)
  }
  finally {
    loading.value = false
    loadingMore.value = false
  }
}

/**
 * 加载下一页
 */
async function loadNextPage() {
  if (loadingMore.value || !hasMore.value)
    return

  pageNumber.value += 1
  await fetchMessages(true)
}

/**
 * 滚动事件处理
 */
function handleScroll() {
  if (!listContainer.value || loadingMore.value || !hasMore.value)
    return

  const { scrollTop, scrollHeight, clientHeight } = listContainer.value

  // 当滚动到距离底部50px时开始加载下一页
  if (scrollTop + clientHeight >= scrollHeight - 50) {
    loadNextPage()
  }
}

/**
 * 刷新消息
 */
async function refreshMessages() {
  pageNumber.value = 1
  hasMore.value = true
  await fetchMessages()
}

/**
 * 添加新消息
 */
function addNewMessage(message) {
  if (!message || !message.content)
    return

  const newMessage = {
    ...message,
    animated: true,
    timestamp: Date.now(),
  }

  messages.value.unshift(newMessage)

  // 滚动到顶部
  nextTick(() => {
    if (listContainer.value) {
      listContainer.value.scrollTop = 0
    }
  })
}

// 生命周期
onMounted(async () => {
  // 保存原始样式
  originalBodyStyle.value = {
    background: document.body.style.background,
    margin: document.body.style.margin,
    padding: document.body.style.padding,
    minHeight: document.body.style.minHeight
  }

  // 设置全屏背景 - 白底
  document.body.style.background = '#ffffff'
  document.body.style.backgroundAttachment = 'fixed'
  document.body.style.margin = '0'
  document.body.style.padding = '0'
  document.body.style.minHeight = '100vh'

  // 同时设置html元素
  document.documentElement.style.background = '#ffffff'
  document.documentElement.style.backgroundAttachment = 'fixed'
  document.documentElement.style.margin = '0'
  document.documentElement.style.padding = '0'
  document.documentElement.style.minHeight = '100vh'

  // 启动时间刷新定时器
  startTimeRefresh()

  await fetchMessages()

  // 添加滚动监听器
  if (listContainer.value) {
    listContainer.value.addEventListener('scroll', handleScroll)
  }

  // 设置 WebSocket 连接
  try {
    const wsUrl = import.meta.env.PROD
      ? '/websocket/sms'
      : 'ws://localhost:18080/websocket/sms'

    wsManager = new WebSocketManager(wsUrl + '?group=PRIVATE_NOTIFY&token=' + accessToken);
    wsManager.connect()

    wsManager.handleMessage = (message) => {
      if (message && message.type === 'sms') {
        addNewMessage(message)
      }
    }
  }
  catch (error) {
    console.error('WebSocket 连接失败:', error)
  }
})

onUnmounted(() => {
  // 停止时间刷新定时器
  stopTimeRefresh()

  // 恢复原始样式
  if (originalBodyStyle.value) {
    document.body.style.background = originalBodyStyle.value.background
    document.body.style.margin = originalBodyStyle.value.margin
    document.body.style.padding = originalBodyStyle.value.padding
    document.body.style.minHeight = originalBodyStyle.value.minHeight

    // 清除html元素样式
    document.documentElement.style.background = ''
    document.documentElement.style.backgroundAttachment = ''
    document.documentElement.style.margin = ''
    document.documentElement.style.padding = ''
    document.documentElement.style.minHeight = ''
  }

  // 移除滚动监听器
  if (listContainer.value) {
    listContainer.value.removeEventListener('scroll', handleScroll)
  }

  if (wsManager) {
    wsManager.disconnect()
  }
})
</script>

<style scoped>
.sms-list-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100vh;
  width: 100%;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  position: relative;
  overflow: hidden;
  z-index: 10;
}

/* PC端优化 */
@media (min-width: 1024px) {
  .sms-list-container {
    /* 在PC端，内容区域居中，但背景占满全屏 */
    align-items: center;
    padding: 0 20px;
  }

  /* 主要内容区域 */
  .sms-list-wrapper,
  .loading-container,
  .no-more-container,
  .toast {
    width: 100%;
    max-width: 480px;
  }
}



/* 简洁白底效果 */
.glass-panel {
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.glass-button {
  background: #f5f5f5;
  border: 1px solid #d0d0d0;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.glass-button:hover {
  background: #e8e8e8;
  border-color: #b0b0b0;
}

.gradient-bg {
  background: #007bff !important;
  color: #ffffff !important;
}

.gradient-spinner {
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-top: 3px solid #007bff;
}



/* 顶部导航栏 */
.top-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  padding: 0 24px;
  background: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  border-radius: 8px 8px 0 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.nav-left,
.nav-right {
  width: 48px;
  display: flex;
  justify-content: center;
}

.nav-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.nav-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border: none;
  background: none;
  color: #333333;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.nav-button:active {
  transform: scale(0.95);
}

.nav-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #000000;
}

/* 短信列表容器 */
.sms-list-wrapper {
  flex: 1;
  width: 100%;
  max-width: 480px;
  margin-bottom: 24px;
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 48px);
}

/* 短信列表 */
.sms-list {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  position: relative;
  z-index: 1;
}

.sms-item {
  margin-bottom: 16px;
  padding: 20px;
  position: relative;
}

.sms-item:hover {
  background: #f8f9fa;
}

/* 短信头部布局 */
.sms-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
}

.sms-phone {
  font-size: 14px;
  color: #333333;
  font-weight: 600;
  font-family: 'Monaco', 'Consolas', monospace;
  display: flex;
  align-items: center;
  gap: 6px;
}

.sms-sender {
  font-size: 14px;
  color: #333333;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
}

.phone-icon,
.sender-icon {
  opacity: 0.7;
  color: #666666;
}

.sms-time {
  font-size: 12px;
  color: #666666;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  background: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.time-icon {
  opacity: 0.8;
  color: #666666;
}

.sms-content {
  margin-bottom: 16px;
}

.sms-line {
  margin-bottom: 8px;
}

.sms-line:last-child {
  margin-bottom: 0;
}

.sms-text {
  font-size: 16px;
  line-height: 1.5;
  color: #000000;
  word-wrap: break-word;
  font-weight: 400;
}

/* 验证码区域 */
.verification-section {
  margin-top: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f2ff 100%);
  border-radius: 12px;
  border: 1px solid #d1e7ff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.verification-code-display {
  text-align: center;
  margin-bottom: 16px;
}

.code-label {
  display: block;
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
  margin-bottom: 8px;
}

.code-value {
  display: block;
  font-size: 32px;
  color: #000000;
  font-weight: 700;
  font-family: 'Monaco', 'Consolas', monospace;
  letter-spacing: 4px;
  line-height: 1.2;
}

.verification-message-content {
  margin-bottom: 16px;
  padding: 10px 12px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  border: 1px solid rgba(0, 123, 255, 0.1);
}

.verification-sms-text {
  font-size: 14px;
  line-height: 1.5;
  color: #495057;
  word-wrap: break-word;
  font-weight: 400;
}

.copy-code-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 12px 20px;
  background: #007bff;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

.copy-code-button:hover {
  background: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.copy-code-button:active {
  transform: translateY(0);
}

.copy-code-button.copied {
  background: #28a745;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

.button-icon {
  display: flex;
  align-items: center;
  z-index: 1;
}

.button-text {
  z-index: 1;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 32px;
  margin: 32px 0;
  text-align: center;
}

.empty-icon {
  margin-bottom: 24px;
  color: #666666;
  animation: pulse 2s ease-in-out infinite;
}

.empty-title {
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: #000000;
}

.empty-description {
  margin: 0;
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 32px;
  margin: 32px 0;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
  margin-bottom: 20px;
}

.loading-text {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #000000;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 加载更多状态 */
.loading-more-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 32px;
}

.loading-more-spinner {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
  margin-bottom: 12px;
}

.loading-more-text {
  margin: 0;
  font-size: 14px;
  color: #666666;
  font-weight: 500;
}

/* 没有更多数据状态 */
.no-more-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px;
}

.no-more-divider {
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #d0d0d0, transparent);
  margin-bottom: 16px;
  border-radius: 1px;
}

.no-more-text {
  margin: 0;
  font-size: 14px;
  color: #666666;
  font-weight: 500;
}

/* Toast 提示 */
.toast {
  position: fixed;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%) translateY(100px);
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  color: #000000;
  background: #ffffff;
  border: 1px solid #d0d0d0;
  border-radius: 6px;
  font-size: 15px;
  font-weight: 600;
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.toast.show {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

.toast-icon {
  display: flex;
  align-items: center;
  color: #28a745;
}

/* 淡入动画 */
.fade-in {
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 滚动条样式 */
.sms-list::-webkit-scrollbar {
  width: 6px;
}

.sms-list::-webkit-scrollbar-track {
  background: transparent;
}

.sms-list::-webkit-scrollbar-thumb {
  background: #d0d0d0;
  border-radius: 3px;
}

.sms-list::-webkit-scrollbar-thumb:hover {
  background: #999999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sms-list-container {
    width: 100%;
    border-radius: 0;
    align-items: stretch;
  }

  .sms-list-container > * {
    max-width: none;
    width: 100%;
  }

  .sms-list-wrapper {
    margin: 0;
    border-radius: 0;
    border-left: none;
    border-right: none;
    margin-bottom: 0;
  }

  .sms-list-wrapper .top-nav {
    border-radius: 0;
    height: 60px;
  }

  .sms-list {
    padding: 16px;
  }

  .sms-item {
    margin-bottom: 12px;
    padding: 16px;
  }

  .nav-title {
    font-size: 18px;
  }

  .sms-text {
    font-size: 15px;
  }

  .verification-section {
    padding: 12px;
  }

  .code-value {
    font-size: 28px;
    letter-spacing: 3px;
  }

  .verification-code-display {
    margin-bottom: 12px;
  }

  .verification-message-content {
    padding: 8px 10px;
    margin-bottom: 12px;
  }

  .verification-sms-text {
    font-size: 13px;
  }

  .copy-code-button {
    padding: 10px 16px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .sms-list-wrapper {
    margin: 0;
    border-radius: 0;
    border-left: none;
    border-right: none;
    margin-bottom: 0;
  }

  .sms-list-wrapper .top-nav {
    border-radius: 0;
    height: 60px;
  }

  .sms-list {
    padding: 12px;
  }

  .nav-title {
    font-size: 16px;
  }

  .verification-code-display {
    text-align: center;
  }
}
</style>
