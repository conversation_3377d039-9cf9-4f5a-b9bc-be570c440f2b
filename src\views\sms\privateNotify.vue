<template>
  <div class="sms-container">
    <!-- 科技风背景 -->
    <div class="tech-background">
      <div class="grid-overlay"></div>
      <div class="glow-orbs">
        <div class="orb orb-1"></div>
        <div class="orb orb-2"></div>
        <div class="orb orb-3"></div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 顶部导航栏 -->
      <header class="tech-header">
        <div class="header-left">
          <button class="tech-btn tech-btn-icon" @click="goBack">
            <i class="icon-arrow-left"></i>
          </button>
          <div class="header-title">
            <h1>短信监控</h1>
            <span class="subtitle">私有通知</span>
          </div>
        </div>
        <div class="header-right">
          <div class="status-indicator" :class="{ active: isConnected }">
            <span class="status-dot"></span>
            <span class="status-text">{{ isConnected ? '在线' : '离线' }}</span>
          </div>
          <button class="tech-btn tech-btn-icon" @click="refreshMessages">
            <i class="icon-refresh" :class="{ spinning: loading }"></i>
          </button>
        </div>
      </header>



      <!-- 消息列表 -->
      <div class="messages-container" ref="listContainer">
        <div class="messages-list">
          <!-- 消息卡片 -->
          <div
            v-for="(message, index) in messages"
            :key="message.id || index"
            class="message-card"
            :class="{ 'new-message': message.animated }"
            @animationend="message.animated = false"
          >
            <!-- 消息头部 -->
            <div class="message-header">
              <div class="sender-info">
                <div class="sender-avatar">
                  <i :class="getSenderIcon(message)"></i>
                </div>
                <div class="sender-details">
                  <div class="sender-name">{{ getSenderName(message) }}</div>
                  <div class="message-time">{{ formatRelativeTime(getMessageTime(message)) }}</div>
                </div>
              </div>
              <div class="message-type-badge" :class="getMessageTypeBadge(message)">
                {{ getMessageType(message) }}
              </div>
            </div>

            <!-- 消息内容 -->
            <div class="message-content">
              <div class="message-text">{{ getMessageText(message) }}</div>

              <!-- 验证码区域 -->
              <div v-if="getVerificationCode(message)" class="verification-zone">
                <div class="verification-header">
                  <span class="verification-label">验证码</span>
                  <div class="verification-indicator"></div>
                </div>
                <div class="verification-display">
                  <span class="verification-code">{{ getVerificationCode(message) }}</span>
                  <button
                    class="copy-btn"
                    :class="{ copied: copiedStates[index] }"
                    @click="copyVerificationCode(message, index)"
                  >
                    <i :class="copiedStates[index] ? 'icon-check' : 'icon-copy'"></i>
                    <span>{{ copiedStates[index] ? '已复制' : '复制' }}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 加载更多 -->
          <div v-if="loadingMore" class="loading-more">
            <div class="tech-spinner"></div>
            <span>加载更多消息...</span>
          </div>

          <!-- 没有更多数据 -->
          <div v-if="!hasMore && messages.length > 0" class="end-indicator">
            <div class="end-line"></div>
            <span>已加载全部内容</span>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="messages.length === 0 && !loading" class="empty-state">
          <div class="empty-icon">
            <i class="icon-message"></i>
          </div>
          <h3>暂无消息</h3>
          <p>等待接收新的短信通知...</p>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <div class="tech-spinner large"></div>
          <p>正在扫描消息...</p>
        </div>
      </div>
    </div>

    <!-- Toast 通知 -->
    <Transition name="toast">
      <div v-if="showToast" class="tech-toast">
        <i class="icon-check"></i>
        <span>验证码已复制到剪贴板</span>
      </div>
    </Transition>
  </div>
</template>

<script setup>
import { nextTick, onMounted, onUnmounted, ref } from 'vue'
import { useAuthStore } from '@/store'
import api from '@/views/sms/sms.js'
import { WebSocketManager } from './WebSocketManager'

// 响应式数据
const messages = ref([])
const copiedStates = ref({})
const loading = ref(false)
const loadingMore = ref(false)
const hasMore = ref(true)
const showToast = ref(false)
const listContainer = ref(null)
const isConnected = ref(false)
const lastUpdateTime = ref(new Date())

// 分页参数
const pageNumber = ref(1)
const pageSize = ref(10)
const smsType = ref('2')

// 时间刷新
const currentTime = ref(new Date())
const timeRefreshInterval = ref(null)

// Store
const { accessToken } = useAuthStore()
let wsManager = null

// 计算属性

// 工具函数
function parseMessageContent(content) {
  if (!content) return []

  const lines = content.split('\n').filter(line => line.trim())
  const result = []

  lines.forEach((line) => {
    const trimmedLine = line.trim()

    if (/^\d{10,11}$/.test(trimmedLine)) {
      result.push({ type: 'phone', content: trimmedLine })
    } else if (/^\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}$/.test(trimmedLine)) {
      result.push({ type: 'time', content: trimmedLine })
    } else {
      result.push({ type: 'message', content: trimmedLine })
    }
  })

  return result
}

function getVerificationCode(message) {
  if (!message?.content) return null

  const patterns = [
    /验证码[为是:：]\s*([A-Z0-9]{4,8})/i,
    /验证码为[:：]?\s*([A-Z0-9]{4,8})/i,
    /code[:\s]+([A-Z0-9]{4,8})/i,
    /：\s*([A-Z0-9]{4,8})/i,
  ]

  for (const pattern of patterns) {
    const match = message.content.match(pattern)
    if (match && match[1]) {
      return match[1].toUpperCase()
    }
  }

  return null
}

function getSenderName(message) {
  const parsed = parseMessageContent(message.content)
  const phone = parsed.find(p => p.type === 'phone')
  return phone ? phone.content : '系统通知'
}

function getSenderIcon(message) {
  const parsed = parseMessageContent(message.content)
  const phone = parsed.find(p => p.type === 'phone')
  return phone ? 'icon-phone' : 'icon-system'
}

function getMessageTime(message) {
  const parsed = parseMessageContent(message.content)
  const time = parsed.find(p => p.type === 'time')
  return time ? time.content : null
}

function getMessageText(message) {
  const parsed = parseMessageContent(message.content)
  const textLines = parsed.filter(p => p.type === 'message')
  return textLines.map(line => line.content).join(' ')
}

function getMessageType(message) {
  return getVerificationCode(message) ? '验证码' : '消息'
}

function getMessageTypeBadge(message) {
  return getVerificationCode(message) ? 'verification' : 'normal'
}



function formatRelativeTime(timeString) {
  if (!timeString) return '刚刚'

  try {
    const messageTime = new Date(timeString)
    const now = currentTime.value
    const diffMs = now.getTime() - messageTime.getTime()
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))

    if (diffMinutes < 1) return '刚刚'
    if (diffMinutes < 60) return `${diffMinutes}分钟前`
    if (diffHours < 24) return `${diffHours}小时前`

    return messageTime.toLocaleDateString()
  } catch {
    return '刚刚'
  }
}

// 业务函数
async function fetchMessages(isLoadMore = false) {
  if (isLoadMore) {
    loadingMore.value = true
  } else {
    loading.value = true
  }

  const params = {
    smsType: smsType.value,
    pageNumber: pageNumber.value,
    pageSize: pageSize.value,
  }

  try {
    const response = await api.getPrivateNotify(params)

    if (response?.data?.records) {
      const newMessages = response.data.records
        .filter(msg => msg && typeof msg.content === 'string')
        .map(msg => ({
          ...msg,
          animated: !isLoadMore,
          timestamp: msg.timestamp || Date.now(),
        }))

      if (isLoadMore) {
        messages.value = [...messages.value, ...newMessages]
      } else {
        messages.value = newMessages
      }

      hasMore.value = pageNumber.value < (response.data.totalPage || 1)
      lastUpdateTime.value = new Date()
    }
  } catch (error) {
    console.error('获取短信失败:', error)
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

async function loadNextPage() {
  if (loadingMore.value || !hasMore.value) return

  pageNumber.value += 1
  await fetchMessages(true)
}

function handleScroll() {
  if (!listContainer.value || loadingMore.value || !hasMore.value) return

  const { scrollTop, scrollHeight, clientHeight } = listContainer.value

  if (scrollTop + clientHeight >= scrollHeight - 50) {
    loadNextPage()
  }
}

async function refreshMessages() {
  pageNumber.value = 1
  hasMore.value = true
  await fetchMessages()
}

function addNewMessage(message) {
  if (!message?.content) return

  const newMessage = {
    ...message,
    animated: true,
    timestamp: Date.now(),
  }

  messages.value.unshift(newMessage)
  lastUpdateTime.value = new Date()

  nextTick(() => {
    if (listContainer.value) {
      listContainer.value.scrollTop = 0
    }
  })
}

async function copyVerificationCode(message, index) {
  const code = getVerificationCode(message)
  if (!code) return

  try {
    await navigator.clipboard.writeText(code)

    copiedStates.value[index] = true
    showToast.value = true

    setTimeout(() => {
      copiedStates.value[index] = false
    }, 2000)

    setTimeout(() => {
      showToast.value = false
    }, 3000)
  } catch (error) {
    console.error('复制失败:', error)
  }
}

function goBack() {
  window.history.back()
}

function startTimeRefresh() {
  if (timeRefreshInterval.value) {
    clearInterval(timeRefreshInterval.value)
  }

  timeRefreshInterval.value = setInterval(() => {
    currentTime.value = new Date()
  }, 60000)
}

function stopTimeRefresh() {
  if (timeRefreshInterval.value) {
    clearInterval(timeRefreshInterval.value)
    timeRefreshInterval.value = null
  }
}

// 生命周期
onMounted(async () => {
  startTimeRefresh()
  await fetchMessages()

  if (listContainer.value) {
    listContainer.value.addEventListener('scroll', handleScroll)
  }

  // WebSocket 连接
  try {
    const wsUrl = import.meta.env.PROD
      ? '/websocket/sms'
      : 'ws://localhost:18080/websocket/sms'

    wsManager = new WebSocketManager(wsUrl + '?group=PRIVATE_NOTIFY&token=' + accessToken)
    wsManager.connect()

    wsManager.handleMessage = (message) => {
      if (message && message.type === 'sms') {
        addNewMessage(message)
      }
    }

    // 监听连接状态
    const checkConnection = () => {
      isConnected.value = wsManager?.socket?.readyState === WebSocket.OPEN
    }

    setInterval(checkConnection, 1000)
  } catch (error) {
    console.error('WebSocket 连接失败:', error)
  }
})

onUnmounted(() => {
  stopTimeRefresh()

  if (listContainer.value) {
    listContainer.value.removeEventListener('scroll', handleScroll)
  }

  if (wsManager) {
    wsManager.disconnect()
  }
})
</script>

<style scoped>
/* 基础样式和变量 */
:root {
  --tech-primary: #00d4ff;
  --tech-secondary: #0099cc;
  --tech-accent: #ff6b35;
  --tech-bg-dark: #0a0e1a;
  --tech-bg-card: #1a1f2e;
  --tech-text-primary: #ffffff;
  --tech-text-secondary: #8892b0;
  --tech-border: #233554;
  --tech-glow: rgba(0, 212, 255, 0.3);
}

/* 图标字体 */
.icon-arrow-left::before { content: '←'; }
.icon-refresh::before { content: '↻'; }
.icon-phone::before { content: '📱'; }
.icon-system::before { content: '🔧'; }
.icon-message::before { content: '💬'; }
.icon-copy::before { content: '📋'; }
.icon-check::before { content: '✓'; }

/* 主容器 */
.sms-container {
  min-height: 100vh;
  background: var(--tech-bg-dark);
  color: var(--tech-text-primary);
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  position: relative;
  overflow: hidden;
}

/* 科技风背景 */
.tech-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 20s linear infinite;
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

.glow-orbs {
  position: absolute;
  width: 100%;
  height: 100%;
}

.orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  animation: float 6s ease-in-out infinite;
}

.orb-1 {
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, var(--tech-primary), transparent);
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.orb-2 {
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, var(--tech-accent), transparent);
  top: 60%;
  right: 20%;
  animation-delay: 2s;
}

.orb-3 {
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, var(--tech-secondary), transparent);
  bottom: 20%;
  left: 60%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(120deg); }
  66% { transform: translateY(10px) rotate(240deg); }
}

/* 主内容区域 */
.main-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;
}

/* 顶部导航栏 */
.tech-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background: rgba(26, 31, 46, 0.9);
  border: 1px solid var(--tech-border);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  margin-bottom: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-title h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: var(--tech-primary);
  text-shadow: 0 0 10px var(--tech-glow);
  letter-spacing: 2px;
}

.header-title .subtitle {
  font-size: 12px;
  color: var(--tech-text-secondary);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 107, 53, 0.1);
  border: 1px solid #ff6b35;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

.status-indicator.active {
  background: rgba(0, 212, 255, 0.1);
  border-color: var(--tech-primary);
  color: var(--tech-primary);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ff6b35;
  animation: pulse-red 2s infinite;
}

.status-indicator.active .status-dot {
  background: var(--tech-primary);
  animation: pulse-blue 2s infinite;
}

@keyframes pulse-red {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(1.2); }
}

@keyframes pulse-blue {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(1.2); }
}

/* 科技按钮 */
.tech-btn {
  background: rgba(26, 31, 46, 0.8);
  border: 1px solid var(--tech-border);
  color: var(--tech-text-primary);
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
  font-size: 14px;
  position: relative;
  overflow: hidden;
}

.tech-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.tech-btn:hover::before {
  left: 100%;
}

.tech-btn:hover {
  border-color: var(--tech-primary);
  box-shadow: 0 0 20px var(--tech-glow);
  transform: translateY(-2px);
}

.tech-btn-icon {
  width: 44px;
  height: 44px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.icon-refresh.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}



/* 消息容器 */
.messages-container {
  background: rgba(26, 31, 46, 0.9);
  border: 1px solid var(--tech-border);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  max-height: 70vh;
  overflow-y: auto;
  position: relative;
}

.messages-list {
  padding: 20px;
}

/* 消息卡片 */
.message-card {
  background: rgba(35, 53, 84, 0.6);
  border: 1px solid var(--tech-border);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.message-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: var(--tech-primary);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.message-card:hover::before {
  opacity: 1;
}

.message-card:hover {
  transform: translateX(5px);
  border-color: var(--tech-primary);
  box-shadow: 0 5px 20px rgba(0, 212, 255, 0.1);
}

.new-message {
  animation: slideInFromTop 0.6s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 消息头部 */
.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--tech-border);
}

.sender-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sender-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--tech-primary), var(--tech-secondary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  box-shadow: 0 0 15px var(--tech-glow);
}

.sender-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sender-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--tech-text-primary);
}

.message-time {
  font-size: 12px;
  color: var(--tech-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.message-type-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 10px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.message-type-badge.verification {
  background: rgba(0, 212, 255, 0.2);
  color: var(--tech-primary);
  border: 1px solid var(--tech-primary);
}

.message-type-badge.normal {
  background: rgba(136, 146, 176, 0.2);
  color: var(--tech-text-secondary);
  border: 1px solid var(--tech-text-secondary);
}

/* 消息内容 */
.message-content {
  line-height: 1.6;
}

.message-text {
  color: var(--tech-text-primary);
  margin-bottom: 15px;
  font-size: 14px;
}

/* 验证码区域 */
.verification-zone {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(0, 153, 204, 0.1));
  border: 1px solid var(--tech-primary);
  border-radius: 10px;
  padding: 20px;
  margin-top: 15px;
  position: relative;
  overflow: hidden;
}

.verification-zone::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent 30%, rgba(0, 212, 255, 0.05) 50%, transparent 70%);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.verification-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.verification-label {
  font-size: 12px;
  color: var(--tech-primary);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.verification-indicator {
  width: 8px;
  height: 8px;
  background: var(--tech-primary);
  border-radius: 50%;
  animation: pulse-blue 2s infinite;
}

.verification-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
}

.verification-code {
  font-size: 28px;
  font-weight: 700;
  color: var(--tech-primary);
  font-family: 'Courier New', monospace;
  letter-spacing: 4px;
  text-shadow: 0 0 10px var(--tech-glow);
  background: rgba(0, 212, 255, 0.1);
  padding: 10px 20px;
  border-radius: 8px;
  border: 1px solid var(--tech-primary);
}

.copy-btn {
  background: linear-gradient(135deg, var(--tech-primary), var(--tech-secondary));
  border: none;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

.copy-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
}

.copy-btn.copied {
  background: linear-gradient(135deg, #00ff88, #00cc6a);
  box-shadow: 0 4px 15px rgba(0, 255, 136, 0.3);
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-state p {
  margin-top: 20px;
  color: var(--tech-text-secondary);
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  padding: 30px;
  color: var(--tech-text-secondary);
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.tech-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 212, 255, 0.1);
  border-top: 3px solid var(--tech-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  box-shadow: 0 0 20px var(--tech-glow);
}

.tech-spinner.large {
  width: 60px;
  height: 60px;
  border-width: 4px;
}

.end-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  color: var(--tech-text-secondary);
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.end-line {
  width: 100px;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--tech-primary), transparent);
  margin-bottom: 15px;
  animation: pulse-line 2s infinite;
}

@keyframes pulse-line {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 64px;
  color: var(--tech-text-secondary);
  margin-bottom: 30px;
  animation: float 3s ease-in-out infinite;
}

.empty-state h3 {
  margin: 0 0 15px 0;
  font-size: 24px;
  color: var(--tech-text-primary);
  text-transform: uppercase;
  letter-spacing: 2px;
}

.empty-state p {
  margin: 0;
  color: var(--tech-text-secondary);
  font-size: 14px;
  line-height: 1.6;
}

/* Toast 通知 */
.tech-toast {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, var(--tech-primary), var(--tech-secondary));
  color: white;
  padding: 15px 25px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from,
.toast-leave-to {
  opacity: 0;
  transform: translateX(-50%) translateY(20px);
}

/* 滚动条样式 */
.messages-container::-webkit-scrollbar {
  width: 8px;
}

.messages-container::-webkit-scrollbar-track {
  background: rgba(35, 53, 84, 0.3);
  border-radius: 4px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--tech-primary), var(--tech-secondary));
  border-radius: 4px;
  box-shadow: 0 0 10px var(--tech-glow);
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--tech-secondary), var(--tech-primary));
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 10px;
  }

  .tech-header {
    padding: 15px 20px;
    flex-direction: column;
    gap: 15px;
  }

  .header-left,
  .header-right {
    width: 100%;
    justify-content: space-between;
  }



  .messages-container {
    max-height: 60vh;
  }

  .message-card {
    padding: 15px;
  }

  .verification-display {
    flex-direction: column;
    gap: 15px;
  }

  .verification-code {
    font-size: 24px;
    letter-spacing: 2px;
    text-align: center;
  }

  .copy-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .header-title h1 {
    font-size: 20px;
  }



  .verification-code {
    font-size: 20px;
    letter-spacing: 1px;
  }

  .message-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .sender-info {
    width: 100%;
  }

  .message-type-badge {
    align-self: flex-end;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --tech-primary: #00ffff;
    --tech-secondary: #ffffff;
    --tech-bg-dark: #000000;
    --tech-bg-card: #111111;
    --tech-border: #ffffff;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>