/**
 * 登录日志API
 */

import { request } from '@/utils'

export default {
  // 分页查询登录日志
  getPageList: params => request.get('/loginLog/pageList', { params }),

  // 根据ID查询登录日志
  getById: id => request.get(`/loginLog/getById/${id}`),

  // 删除登录日志
  deleteById: id => request.delete(`/loginLog/delete/${id}`),

  // 批量删除登录日志
  deleteBatch: ids => request.delete('/loginLog/deleteBatch', { data: ids }),

  // 清空登录日志
  clearAll: () => request.delete('/loginLog/clearAll'),
}
