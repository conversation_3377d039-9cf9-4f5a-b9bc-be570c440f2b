<template>
  <div class="space-gallery">
    <!-- 宇宙背景效果 -->
    <div class="universe-background">
      <div v-for="n in 100" :key="`star-${n}`" class="star" :style="getRandomStarStyle()" />
      <div v-for="n in 5" :key="`planet-${n}`" class="planet" :style="getRandomPlanetStyle()">
        <div v-if="Math.random() > 0.5" class="planet-ring" />
      </div>
      <div v-for="n in 8" :key="`comet-${n}`" class="comet" :style="getRandomCometStyle()">
        <div class="comet-tail" />
      </div>
      <div v-for="n in 3" :key="`nebula-${n}`" class="nebula" :style="getRandomNebulaStyle()" />
    </div>

    <!-- 上部分：大图展示区域 -->
    <div class="showcase-section">
      <transition name="warp-speed" mode="out-in">
        <div
          v-if="currentPhoto"
          :key="currentPhoto.id"
          class="spaceship-viewport"
        >
          <!-- 光影效果层 -->
          <div class="space-effects">
            <div class="wormhole" />
            <div class="stardust" />
            <div class="cosmic-rays">
              <div v-for="n in 15" :key="`ray-${n}`" class="cosmic-ray" :style="getRandomRayStyle()" />
            </div>
          </div>

          <!-- 大图 - 太空舱窗口 -->
          <div class="spaceship-window" :class="{ portrait: isPortrait(currentPhoto) }">
            <div class="window-frame">
              <div class="window-glass">
                <img :src="currentPhoto.url" :alt="currentPhoto.description || '宇宙中的回忆'" @load="checkOrientation">
              </div>
              <div class="control-panel">
                <div class="panel-light" :class="{ blink: animationReady }" />
                <div class="photo-date">
                  {{ formatDate(currentPhoto.createTime || new Date()) }}
                </div>
                <div class="photo-description">
                  {{ currentPhoto.description || '漂浮在宇宙中的回忆' }}
                </div>
              </div>
            </div>
          </div>

          <!-- 导航按钮 -->
          <div class="nav-rocket prev" @click="prevPhoto">
            <span class="rocket-icon">◂</span>
            <span class="rocket-flame" />
          </div>
          <div class="nav-rocket next" @click="nextPhoto">
            <span class="rocket-icon">▸</span>
            <span class="rocket-flame" />
          </div>
        </div>
      </transition>
    </div>

    <!-- 下部分：缩略图展示区域 -->
    <div class="mission-control">
      <div class="control-panel-bg" />
      <div class="mission-scroll">
        <div
          v-for="(photo, index) in photos"
          :key="photo.id"
          class="mission-log"
          :class="{ active: currentPhoto && currentPhoto.id === photo.id }"
          @click="selectPhoto(index)"
        >
          <div class="log-frame">
            <div class="log-screen">
              <img :src="photo.url" :alt="`任务日志 ${index + 1}`">
            </div>
            <div class="log-date">
              {{ formatDate(photo.createTime || new Date()).split('.')[0] }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 全屏预览 -->
    <div v-if="fullscreenMode" class="galaxy-view" @click="closeFullscreen">
      <div class="galaxy-content" @click.stop>
        <div class="galaxy-controls">
          <div class="galaxy-close" @click="closeFullscreen">
            ✖
          </div>
          <div v-if="photos.length > 1" class="prev galaxy-nav" @click="prevPhoto">
            ◂
          </div>
          <div v-if="photos.length > 1" class="galaxy-nav next" @click="nextPhoto">
            ▸
          </div>
        </div>
        <NImage
          v-if="currentPhoto"
          :src="currentPhoto.url"
          class="galaxy-image"
          :preview-disabled="true"
          object-fit="contain"
        />
        <div v-if="currentPhoto" class="galaxy-caption">
          <div class="galaxy-date">
            {{ formatDate(currentPhoto.createTime || new Date()) }}
          </div>
          <div class="galaxy-description">
            {{ currentPhoto.description || '宇宙中的珍贵回忆' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { NImage } from 'naive-ui'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import loveApi from './api.js'

// 状态管理
const photos = ref([])
const currentPhotoIndex = ref(0)
const currentPhoto = computed(() => photos.value[currentPhotoIndex.value] || null)
const fullscreenMode = ref(false)
const isPhotoPortrait = ref(false)
const animationReady = ref(false)

// 图片方向检测
function checkOrientation(e) {
  if (e && e.target) {
    const img = e.target
    isPhotoPortrait.value = img.naturalHeight > img.naturalWidth
  }
}

function isPortrait(_photo) {
  return isPhotoPortrait.value
}

// 照片操作
async function fetchPhotos() {
  try {
    const res = await loveApi.pageList({ pageNumber: 1, pageSize: 20 })
    if (res.code === 200) {
      photos.value = res.data.records
      setTimeout(() => {
        animationReady.value = true
      }, 500)
    }
  }
  catch (error) {
    console.error('获取照片失败:', error)
  }
}

function selectPhoto(index) {
  currentPhotoIndex.value = index
}

function prevPhoto() {
  currentPhotoIndex.value = (currentPhotoIndex.value - 1 + photos.value.length) % photos.value.length
}

function nextPhoto() {
  currentPhotoIndex.value = (currentPhotoIndex.value + 1) % photos.value.length
}

function _openFullscreen() {
  fullscreenMode.value = true
}

function closeFullscreen() {
  fullscreenMode.value = false
}

// 自动轮播
let autoSlideTimer = null

function startAutoSlide() {
  stopAutoSlide()
  autoSlideTimer = setInterval(() => {
    nextPhoto()
  }, 5000)
}

function stopAutoSlide() {
  if (autoSlideTimer) {
    clearInterval(autoSlideTimer)
    autoSlideTimer = null
  }
}

// 工具函数
function formatDate(dateString) {
  const date = new Date(dateString)
  return `${date.getFullYear()}.${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getDate().toString().padStart(2, '0')}`
}

// 太空元素随机样式生成
function getRandomStarStyle() {
  const size = Math.random() * 3 + 1
  const x = Math.random() * 100
  const y = Math.random() * 100
  const duration = Math.random() * 10 + 3
  const delay = Math.random() * -10

  return {
    width: `${size}px`,
    height: `${size}px`,
    left: `${x}%`,
    top: `${y}%`,
    animationDuration: `${duration}s`,
    animationDelay: `${delay}s`,
    opacity: Math.random() * 0.7 + 0.3,
  }
}

function getRandomPlanetStyle() {
  const size = Math.random() * 60 + 20
  const colorHue = Math.floor(Math.random() * 360)
  const saturation = Math.floor(Math.random() * 50) + 50
  const lightness = Math.floor(Math.random() * 40) + 10

  return {
    width: `${size}px`,
    height: `${size}px`,
    left: `${Math.random() * 100}%`,
    top: `${Math.random() * 100}%`,
    background: `radial-gradient(circle at 30% 30%, 
                 hsl(${colorHue}, ${saturation}%, ${lightness + 30}%), 
                 hsl(${colorHue}, ${saturation}%, ${lightness}%))`,
    animationDuration: `${Math.random() * 100 + 80}s`,
    opacity: Math.random() * 0.5 + 0.2,
  }
}

function getRandomCometStyle() {
  const size = Math.random() * 5 + 2
  const angle = Math.random() * 60 - 30
  const duration = Math.random() * 6 + 2
  const delay = Math.random() * -20

  return {
    '--size': `${size}px`,
    '--angle': `${angle}deg`,
    '--duration': `${duration}s`,
    '--delay': `${delay}s`,
    'left': `${Math.random() * 100}%`,
    'top': `${Math.random() * 40}%`,
  }
}

function getRandomNebulaStyle() {
  const size = Math.random() * 300 + 200
  const hue1 = Math.floor(Math.random() * 360)
  const hue2 = (hue1 + 60) % 360

  return {
    width: `${size}px`,
    height: `${size}px`,
    left: `${Math.random() * 100}%`,
    top: `${Math.random() * 100}%`,
    background: `radial-gradient(circle, 
                 hsla(${hue1}, 70%, 60%, 0.15), 
                 hsla(${hue2}, 70%, 40%, 0.05))`,
    transform: `rotate(${Math.random() * 360}deg)`,
    animationDuration: `${Math.random() * 100 + 100}s`,
  }
}

function getRandomRayStyle() {
  const length = Math.random() * 50 + 30
  const width = Math.random() * 2 + 0.5
  const x = Math.random() * 100
  const y = Math.random() * 100
  const angle = Math.random() * 360
  const duration = Math.random() * 5 + 2
  const delay = Math.random() * 5

  return {
    height: `${length}px`,
    width: `${width}px`,
    left: `${x}%`,
    top: `${y}%`,
    transform: `rotate(${angle}deg)`,
    animationDuration: `${duration}s`,
    animationDelay: `${delay}s`,
  }
}

// 生命周期钩子
onMounted(async () => {
  await fetchPhotos()

  // 启动自动轮播
  setTimeout(() => {
    startAutoSlide()
  }, 3000)
})

onUnmounted(() => {
  stopAutoSlide()
})

// 监听当前照片变化，重新检测方向
watch(currentPhotoIndex, () => {
  isPhotoPortrait.value = false // 先重置
  // 方向检测会在img的load事件中进行
})
</script>

<style scoped>
.space-gallery {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: linear-gradient(135deg, #000510, #02072e, #1a0933);
  color: #fff;
}

/* 宇宙背景效果 */
.universe-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  overflow: hidden;
}

.star {
  position: absolute;
  border-radius: 50%;
  background-color: #fff;
  animation: twinkle 5s infinite ease-in-out;
}

@keyframes twinkle {
  0%,
  100% {
    opacity: var(--opacity, 0.7);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.planet {
  position: absolute;
  border-radius: 50%;
  box-shadow: inset 3px -3px 10px rgba(0, 0, 0, 0.6);
  animation: orbit 100s linear infinite;
  z-index: 1;
}

.planet-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(75deg);
  width: 140%;
  height: 20%;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
}

@keyframes orbit {
  0% {
    transform: translateX(-50px) translateY(20px);
  }
  25% {
    transform: translateX(30px) translateY(-40px);
  }
  50% {
    transform: translateX(70px) translateY(0px);
  }
  75% {
    transform: translateX(0px) translateY(50px);
  }
  100% {
    transform: translateX(-50px) translateY(20px);
  }
}

.comet {
  position: absolute;
  width: var(--size);
  height: var(--size);
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  animation: comet-move var(--duration) linear infinite;
  animation-delay: var(--delay);
  z-index: 2;
}

.comet-tail {
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 3px;
  background: linear-gradient(to left, rgba(255, 255, 255, 0.8), transparent);
  transform: rotate(var(--angle));
  transform-origin: left center;
}

@keyframes comet-move {
  0% {
    transform: translateX(120vw) translateY(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(-100px) translateY(calc(var(--angle) * 1px));
    opacity: 0;
  }
}

.nebula {
  position: absolute;
  border-radius: 50%;
  filter: blur(30px);
  animation: rotate-nebula 150s linear infinite;
  opacity: 0.3;
}

@keyframes rotate-nebula {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.1);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

/* 上部分：大图展示区域 */
.showcase-section {
  flex: 1;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px;
  min-height: 70%;
  overflow: hidden;
}

.spaceship-viewport {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.spaceship-window {
  position: relative;
  max-width: 85%;
  max-height: 85%;
  z-index: 10;
  perspective: 1000px;
}

.spaceship-window.portrait {
  height: 85%;
  width: auto;
}

.window-frame {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 20px;
  background: #0a0e17;
  padding: 15px;
  box-shadow:
    0 0 0 6px #1a2035,
    0 0 0 8px #2c3858,
    0 0 20px rgba(0, 195, 255, 0.3),
    0 0 40px rgba(0, 89, 255, 0.2);
  transform-style: preserve-3d;
  transform: rotateX(5deg);
}

.window-glass {
  flex: 1;
  border-radius: 15px 15px 5px 5px;
  overflow: hidden;
  background: rgba(0, 40, 80, 0.2);
  border: 2px solid #2c3858;
  box-shadow: inset 0 0 20px rgba(0, 149, 255, 0.2);
}

.window-glass img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.5s cubic-bezier(0.23, 1, 0.32, 1);
}

.spaceship-window:hover .window-glass img {
  transform: scale(1.03);
}

.control-panel {
  height: 40px;
  display: flex;
  align-items: center;
  margin-top: 10px;
  padding: 0 15px;
  background: #161e2e;
  border-radius: 5px;
  border: 1px solid #2c3858;
}

.panel-light {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #00ff9d;
  margin-right: 15px;
  box-shadow: 0 0 10px #00ff9d;
}

.panel-light.blink {
  animation: blink 2s infinite;
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

.photo-date {
  font-size: 14px;
  color: #7af1ff;
  margin-right: 15px;
  font-family: 'Courier New', monospace;
}

.photo-description {
  flex: 1;
  font-size: 14px;
  color: #e0f7ff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 导航按钮 */
.nav-rocket {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 20;
}

.rocket-icon {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(10, 20, 40, 0.7);
  color: #7af1ff;
  font-size: 20px;
  border-radius: 10px;
  box-shadow: 0 0 15px rgba(0, 149, 255, 0.4);
  transition: all 0.3s;
}

.rocket-flame {
  position: absolute;
  width: 20px;
  height: 30px;
  background: radial-gradient(ellipse at top, #7af1ff, #0066ff, transparent 70%);
  opacity: 0;
  transition: all 0.3s;
}

.nav-rocket.prev {
  left: 30px;
}

.nav-rocket.next {
  right: 30px;
}

.nav-rocket.prev .rocket-flame {
  left: -15px;
  transform: rotate(-90deg);
}

.nav-rocket.next .rocket-flame {
  right: -15px;
  transform: rotate(90deg);
}

.nav-rocket:hover .rocket-icon {
  transform: scale(1.1);
  background: rgba(0, 80, 120, 0.8);
}

.nav-rocket:hover .rocket-flame {
  opacity: 0.8;
  animation: flame 0.5s infinite alternate;
}

@keyframes flame {
  0% {
    height: 25px;
    opacity: 0.6;
  }
  100% {
    height: 35px;
    opacity: 0.9;
  }
}

/* 空间特效 */
.space-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 5;
  pointer-events: none;
}

.wormhole {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 1px;
  height: 1px;
  background: transparent;
  box-shadow: 0 0 250px 150px rgba(76, 0, 255, 0.1);
  animation: pulse-wormhole 15s infinite ease-in-out;
}

@keyframes pulse-wormhole {
  0%,
  100% {
    box-shadow: 0 0 250px 150px rgba(76, 0, 255, 0.1);
  }
  50% {
    box-shadow: 0 0 350px 200px rgba(0, 204, 255, 0.15);
  }
}

.stardust {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="white"/></svg>')
    repeat;
  opacity: 0.2;
  animation: drift 60s linear infinite;
}

@keyframes drift {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 1000px 500px;
  }
}

.cosmic-rays {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.cosmic-ray {
  position: absolute;
  background: linear-gradient(to bottom, rgba(120, 200, 255, 0.8), rgba(120, 200, 255, 0));
  border-radius: 1px;
  animation: cosmic-pulse 3s infinite ease-in-out;
}

@keyframes cosmic-pulse {
  0%,
  100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.8;
  }
}

/* 下部分：任务控制中心 */
.mission-control {
  position: relative;
  height: 160px;
  z-index: 15;
}

.control-panel-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(10, 20, 40, 0.7), rgba(20, 30, 60, 0.8));
  backdrop-filter: blur(5px);
  border-top: 2px solid rgba(44, 56, 88, 0.8);
  box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.3);
}

.mission-scroll {
  position: relative;
  display: flex;
  overflow-x: auto;
  padding: 15px 20px;
  gap: 15px;
  height: 100%;
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: #0066ff rgba(0, 20, 40, 0.5);
}

.mission-scroll::-webkit-scrollbar {
  height: 6px;
}

.mission-scroll::-webkit-scrollbar-track {
  background: rgba(0, 20, 40, 0.5);
  border-radius: 10px;
}

.mission-scroll::-webkit-scrollbar-thumb {
  background: #0066ff;
  border-radius: 10px;
}

.mission-log {
  flex: 0 0 auto;
  height: 120px;
  width: 90px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.log-frame {
  width: 100%;
  height: 100%;
  background: #0e1623;
  border-radius: 8px;
  padding: 5px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  border: 1px solid #2c3858;
  overflow: hidden;
  transition: all 0.3s;
}

.log-screen {
  width: 100%;
  height: calc(100% - 20px);
  border-radius: 5px;
  overflow: hidden;
  background: rgba(0, 20, 40, 0.3);
  border: 1px solid #161e2e;
}

.log-screen img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.log-date {
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #7af1ff;
  font-family: 'Courier New', monospace;
}

.mission-log:hover .log-frame {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.6);
  border-color: #3e5282;
}

.mission-log:hover .log-screen img {
  transform: scale(1.1);
}

.mission-log.active .log-frame {
  background: #192640;
  border-color: #0066ff;
  box-shadow: 0 0 15px rgba(0, 119, 255, 0.4);
}

/* 全屏预览 */
.galaxy-view {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 5, 20, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: warpIn 0.3s forwards;
}

.galaxy-content {
  position: relative;
  max-width: 90%;
  max-height: 90%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.galaxy-controls {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  z-index: 10;
}

.galaxy-close,
.galaxy-nav {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(10, 30, 60, 0.7);
  color: #7af1ff;
  font-size: 20px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.galaxy-close:hover,
.galaxy-nav:hover {
  background: rgba(0, 80, 120, 0.8);
  box-shadow: 0 0 15px rgba(0, 149, 255, 0.4);
  transform: scale(1.1);
}

.galaxy-image {
  max-width: 100%;
  max-height: 80vh;
  border-radius: 10px;
  box-shadow: 0 0 30px rgba(0, 119, 255, 0.3);
  animation: zoomWarp 0.5s;
}

.galaxy-caption {
  margin-top: 20px;
  color: white;
  text-align: center;
  max-width: 600px;
}

.galaxy-date {
  font-size: 14px;
  color: #7af1ff;
  margin-bottom: 10px;
  font-family: 'Courier New', monospace;
}

.galaxy-description {
  font-size: 16px;
  color: #e0f7ff;
}

/* 过渡动画 */
.warp-speed-enter-active,
.warp-speed-leave-active {
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
}

.warp-speed-enter-from {
  opacity: 0;
  transform: scale(0.8) translateZ(-100px);
}

.warp-speed-leave-to {
  opacity: 0;
  transform: scale(1.2) translateZ(100px);
}

@keyframes warpIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(10px);
  }
}

@keyframes zoomWarp {
  from {
    transform: scale(0.5);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .showcase-section {
    padding: 15px;
  }

  .nav-rocket {
    width: 40px;
    height: 40px;
  }

  .nav-rocket.prev {
    left: 10px;
  }

  .nav-rocket.next {
    right: 10px;
  }

  .mission-control {
    height: 120px;
  }

  .mission-log {
    height: 90px;
    width: 70px;
  }
}
</style>
