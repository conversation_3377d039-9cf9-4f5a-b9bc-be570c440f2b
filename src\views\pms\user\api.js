/**********************************
 * @Author: <PERSON>
 * @LastEditor: <PERSON>
 * @LastEditTime: 2023/12/05 21:29:51
 * @Email: <EMAIL>
 * Copyright © 2023 Ronnie <PERSON>(大脸怪) | https://isme.top
 **********************************/

import { request } from '@/utils'

export default {
  // create: data => request.post('/user', data),
  create: data => request.post('/user/register', data),
  // read: (params = {}) => request.get('/user', { params }),
  read: (params = {}) => request.get('/user/list', { params }),
  // update: data => request.patch(`/user/${data.id}`, data),
  update: data => request.post(`/user/updateRole`, data),
  updateEnable: data => request.post(`/user/updateEnable`, data),
  // delete: id => request.delete(`/user/${id}`),
  delete: id => request.post(`/user/delete/${id}`),
  // resetPwd: (id, data) => request.patch(`/user/password/reset/${id}`, data),
  resetPwd: (id, data) => request.post(`/user/resetPassword`, data),

  // getAllRoles: () => request.get('/role?enable=1'),
  getAllRoles: () => request.get('/role/listBase'),
}
