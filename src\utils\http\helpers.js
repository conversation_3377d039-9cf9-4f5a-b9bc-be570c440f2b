/**********************************
 * @FilePath: helpers.js
 * @Author: <PERSON>
 * @LastEditor: <PERSON>
 * @LastEditTime: 2023/12/04 22:46:22
 * @Email: <EMAIL>
 * Copyright © 2023 Ronnie <PERSON>(大脸怪) | https://isme.top
 **********************************/

import { useAuthStore } from '@/store'

let isConfirming = false
export function resolveResError(code, msg, needTip = true) {
  switch (code) {
    case 401:
      if (isConfirming || !needTip) {
        return
      }
      isConfirming = true
      $dialog.confirm({
        title: '提示',
        type: 'info',
        content: '登录已过期，是否重新登录？',
        confirm() {
          useAuthStore().logout()
          window.$message?.success('已退出登录')
          isConfirming = false
        },
        cancel() {
          isConfirming = false
        },
      })
      return false
    case 11008:
      if (isConfirming || !needTip)
        return
      isConfirming = true
      $dialog.confirm({
        title: '提示',
        type: 'info',
        content: `${msg}，是否重新登录？`,
        confirm() {
          useAuthStore().logout()
          window.$message?.success('已退出登录')
          isConfirming = false
        },
        cancel() {
          isConfirming = false
        },
      })
      return false
    case 403:
      msg = '请求被拒绝'
      break
    case 404:
      msg = '请求资源或接口不存在'
      break
    case 500:
      msg = msg ?? '服务器发生异常'
      break
    default:
      msg = msg ?? `【${code}】: 未知异常!`
      break
  }
  needTip && window.$message?.error(msg)
  return msg
}
