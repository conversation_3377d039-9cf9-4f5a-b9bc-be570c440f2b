<template>
  <div class="gallery-container">
    <!-- 背景动画 -->
    <div class="background-animation">
      <div v-for="n in 20" :key="`heart-${n}`" class="floating-item heart" :style="getRandomStyle()">
        ❤
      </div>
      <div v-for="n in 15" :key="`star-${n}`" class="floating-item star" :style="getRandomStyle()">
        ✨
      </div>
    </div>

    <div
      id="box"
      ref="boxRef"
      :style="boxStyle"
      @mousedown.prevent="handleMouseDown"
    >
      <div
        v-for="(item, index) in images"
        :key="item.id"
        :style="getDivStyle(index, item)"
        :class="{ 'animate-ready': animationReady }"
        @click="handleImageClick(index)"
      />
      <p />
    </div>

    <!-- 图片预览 -->
    <NImageGroup>
      <NImage
        v-if="currentImage"
        v-model:show="showPreview"
        :src="currentImage.url"
        :width="800"
        object-fit="contain"
        class="preview-image"
      />
    </NImageGroup>
  </div>
</template>

<script setup>
import { NImage, NImageGroup } from 'naive-ui'
import { computed, onMounted, onUnmounted, ref } from 'vue'
import loveApi from './api.js'

const boxRef = ref(null)
const images = ref([])
const perspective = ref(1000)
const animationReady = ref(false)
const rotating = ref(false)
const rotation = ref({ x: -10, y: 0 })

const showPreview = ref(false)
const currentImage = ref(null)

const SENSITIVITY = 0.1
const INERTIA_STRENGTH = 8
const FRICTION = 0.8

const boxStyle = computed(() => ({
  transform: `rotateX(${rotation.value.x}deg) rotateY(${rotation.value.y}deg)`,
}))

let inertiaTimer = null
let lastMousePosition = { x: 0, y: 0 }
let velocity = { x: 0, y: 0 }
let lastTime = 0

async function fetchImages() {
  try {
    const res = await loveApi.pageList({ pageNumber: 1, pageSize: 10 })
    if (res.code === 200) {
      images.value = res.data.records
      setTimeout(() => {
        animationReady.value = true
      }, 100)
    }
  }
  catch (error) {
    console.error('Failed to fetch images:', error)
  }
}

function getDivStyle(index, item) {
  return {
    background: `url(${item.url}) center/cover`,
    transform: animationReady.value
      ? `rotateY(${index * 36}deg) translate3d(0,0,350px)`
      : 'rotateY(0deg) translate3d(0,0,0)',
  }
}

function getRandomStyle() {
  const size = Math.random() * 20 + 10
  const startX = Math.random() * 100
  const duration = Math.random() * 10 + 15
  const delay = Math.random() * -20

  return {
    '--size': `${size}px`,
    '--start-x': `${startX}%`,
    '--duration': `${duration}s`,
    '--delay': `${delay}s`,
    'left': `${Math.random() * 100}%`,
  }
}

function handleImageClick(index) {
  if (!rotating.value) {
    currentImage.value = images.value[index]
    showPreview.value = true
  }
}

function handleMouseDown(e) {
  if (inertiaTimer) {
    clearInterval(inertiaTimer)
    inertiaTimer = null
  }

  rotating.value = true
  lastMousePosition = { x: e.clientX, y: e.clientY }
  lastTime = Date.now()
  velocity = { x: 0, y: 0 }

  const moveHandler = (e) => {
    if (!rotating.value)
      return

    const currentTime = Date.now()
    const deltaTime = currentTime - lastTime
    const deltaX = e.clientX - lastMousePosition.x
    const deltaY = e.clientY - lastMousePosition.y

    velocity.x = deltaX / deltaTime
    velocity.y = deltaY / deltaTime

    rotation.value.y += deltaX * SENSITIVITY
    rotation.value.x -= deltaY * SENSITIVITY

    lastMousePosition = { x: e.clientX, y: e.clientY }
    lastTime = currentTime
  }

  const upHandler = () => {
    rotating.value = false
    document.removeEventListener('mousemove', moveHandler)
    document.removeEventListener('mouseup', upHandler)

    inertiaTimer = setInterval(() => {
      velocity.x *= FRICTION
      velocity.y *= FRICTION

      rotation.value.y += velocity.x * INERTIA_STRENGTH
      rotation.value.x -= velocity.y * INERTIA_STRENGTH

      if (Math.abs(velocity.x) < 0.01 && Math.abs(velocity.y) < 0.01) {
        clearInterval(inertiaTimer)
        inertiaTimer = null
      }
    }, 16)
  }

  document.addEventListener('mousemove', moveHandler)
  document.addEventListener('mouseup', upHandler)
}

let index = 0

function handleWheel(e) {
  e.preventDefault()
  const d = e.wheelDelta / 120 || -e.detail / 3

  if (d > 0)
    index -= 20
  else
    index += 30

  index = Math.max(-1050, index)
  perspective.value = 1000 + index
}

onMounted(async () => {
  await fetchImages()

  document.addEventListener('wheel', handleWheel, { passive: false })

  if (document.onmousewheel !== null)
    document.addEventListener('DOMMouseScroll', handleWheel, { passive: false })
})

onUnmounted(() => {
  if (inertiaTimer)
    clearInterval(inertiaTimer)

  document.removeEventListener('wheel', handleWheel)
  document.removeEventListener('DOMMouseScroll', handleWheel)
})
</script>

<style scoped>
.gallery-container {
  height: 100%;
  overflow: hidden;
  display: flex;
  background: #000;
  perspective: v-bind('`${perspective}px`');
  transform-style: preserve-3d;
}

#box {
  position: relative;
  display: flex;
  width: 130px;
  height: 200px;
  margin: auto;
  transform-style: preserve-3d;
  will-change: transform;
}

#box > div {
  transform-style: preserve-3d;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  line-height: 200px;
  font-size: 50px;
  text-align: center;
  box-shadow: 0 0 10px #fff;
  -webkit-box-reflect: below 10px -webkit-linear-gradient(top, rgba(0, 0, 0, 0) 40%, rgba(0, 0, 0, 0.8) 100%);
  will-change: transform;
  cursor: pointer;
  transition: box-shadow 0.3s;
}

#box > div:hover {
  box-shadow: 0 0 20px rgba(255, 192, 203, 0.8);
}

#box > div {
  transition: none;
}

#box > div.animate-ready {
  transition: transform 1s;
}

#box > div.animate-ready:nth-child(1) {
  transition-delay: 2s;
}

#box > div.animate-ready:nth-child(2) {
  transition-delay: 1.8s;
}

#box > div.animate-ready:nth-child(3) {
  transition-delay: 1.6s;
}

#box > div.animate-ready:nth-child(4) {
  transition-delay: 1.4s;
}

#box > div.animate-ready:nth-child(5) {
  transition-delay: 1.2s;
}

#box > div.animate-ready:nth-child(6) {
  transition-delay: 1s;
}

#box > div.animate-ready:nth-child(7) {
  transition-delay: 0.8s;
}

#box > div.animate-ready:nth-child(8) {
  transition-delay: 0.6s;
}

#box > div.animate-ready:nth-child(9) {
  transition-delay: 0.4s;
}

#box > div.animate-ready:nth-child(10) {
  transition-delay: 0.2s;
}

#box p {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  width: 1200px;
  height: 1200px;
  background: -webkit-radial-gradient(center center, 600px 600px, rgba(50, 50, 50, 1), rgba(0, 0, 0, 0));
  border-radius: 50%;
  transform: rotateX(90deg) translate3d(-600px, 0, -105px);
}

.background-animation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
  z-index: -1;
}

.floating-item {
  position: absolute;
  width: var(--size);
  height: var(--size);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: float var(--duration) linear infinite;
  animation-delay: var(--delay);
  opacity: 0.6;
}

.heart {
  color: rgba(255, 105, 180, 0.6);
  font-size: calc(var(--size) * 1.2);
}

.star {
  color: rgba(255, 255, 255, 0.8);
  font-size: calc(var(--size) * 0.8);
}

@keyframes float {
  0% {
    transform: translate(calc(var(--start-x) - 10%), 120vh) rotate(0deg);
    opacity: 0;
  }
  20% {
    opacity: 0.6;
  }
  80% {
    opacity: 0.6;
  }
  100% {
    transform: translate(var(--start-x), -20vh) rotate(360deg);
    opacity: 0;
  }
}

:deep(.n-image-preview) {
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(.n-image-preview-container) {
  display: flex !important;
  justify-content: center;
  align-items: center;
}

:deep(.n-image-preview-overlay) {
  background: rgba(0, 0, 0, 0.85);
}

:deep(.n-image) {
  max-width: 90vw;
  max-height: 90vh;
}

:deep(.n-image img) {
  max-height: 90vh;
  object-fit: contain;
}

:deep(.n-image-preview-wrapper) {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: 100vh;
}

:deep(.n-image-preview) {
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-image {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2000;
}
</style>
