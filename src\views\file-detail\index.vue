<template>
  <CommonPage>
    <template #action>
      <NButton type="primary" @click="handleAdd()">
        <i class="i-material-symbols:add mr-4 text-18" />
        新增文件记录表
      </NButton>
    </template>

    <MeCrud
      ref="$table"
      v-model:query-items="queryItems"
      :scroll-x="1200"
      :columns="columns"
      :get-data="fileDetailApi.pageList"
    >
      <MeQueryItem label="文件名称" :label-width="80">
        <n-input v-model:value="queryItems.originalFilename" type="text" placeholder="请输入文件名称" clearable />
      </MeQueryItem>
    </MeCrud>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeQueryItem } from '@/components'
import { CommonPage } from '@/components/index.js'
import { useCrud } from '@/composables'
import { NButton, NImage } from 'naive-ui'
import fileDetail<PERSON>pi from './fileDetailApi'

defineOptions({ name: 'FileDetailMgt' })

const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})

onMounted(() => {
  $table.value?.handleSearch()
})

const { handleAdd, handleDelete, handleEdit }
  = useCrud({
    name: '文件记录表',
    doCreate: fileDetailApi.create,
    doDelete: fileDetailApi.delete,
    doUpdate: fileDetailApi.update,
    initForm: { enable: true },
    refresh: (_, keepCurrentPage) => $table.value?.handleSearch(keepCurrentPage),
  })

const columns = [
  {
    title: '文件地址',
    key: 'url',
    render(row) {
      if (row.ext === 'png' || row.ext === 'jpg' || row.ext === 'jpeg' || row.ext === 'gif') {
        return h(NImage, { width: 40, src: row.thUrl, previewSrc: row.url })
      }
      return h('span', row.url)
    },
  },
  { title: '文件大小', key: 'size' },
  { title: '文件名', key: 'originalFilename' },
  { title: '文件扩展名', key: 'ext' },
  { title: '存储平台', key: 'platform' },
  {
    title: '操作',
    key: 'actions',
    width: 320,
    align: 'right',
    fixed: 'right',
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            style: 'margin-left: 12px;',
            disabled: row.code === 'SUPER_ADMIN',
            onClick: () => handleEdit(row),
          },
          {
            default: () => '编辑',
            icon: () => h('i', { class: 'i-material-symbols:edit-outline text-14' }),
          },
        ),

        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            disabled: row.code === 'SUPER_ADMIN',
            onClick: () => handleDelete(row.id),
          },
          {
            default: () => '删除',
            icon: () => h('i', { class: 'i-material-symbols:delete-outline text-14' }),
          },
        ),
      ]
    },
  },
]
</script>
