<template>
  <CommonPage>
    <template #action>
      <NButton type="primary" @click="handleAdd()">
        <i class="i-material-symbols:add mr-4 text-18" />
        新增字典
      </NButton>
    </template>

    <MeCrud
      ref="$table"
      v-model:query-items="queryItems"
      :scroll-x="1200"
      :columns="columns"
      :get-data="dictApi.pageList"
    >
      <MeQueryItem label="字典编码" :label-width="80">
        <n-input v-model:value="queryItems.code" type="text" placeholder="请输入字典编码" clearable />
      </MeQueryItem>
      <MeQueryItem label="字典名称" :label-width="80">
        <n-input v-model:value="queryItems.codeName" type="text" placeholder="请输入字典名称" clearable />
      </MeQueryItem>
    </MeCrud>
    <MeModal ref="modalRef" width="520px">
      <n-form
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="80"
        :model="modalForm"
      >
        <n-form-item
          label="字典编码"
          path="code"
          :rule="{
            required: true,
            message: '请输入字典编码',
            trigger: ['input', 'blur'],
          }"
        >
          <n-input v-model:value="modalForm.code" />
        </n-form-item>
        <n-form-item
          label="字典名称"
          path="codeName"
          :rule="{
            required: true,
            message: '请输入字典名称',
            trigger: ['input', 'blur'],
          }"
        >
          <n-input v-model:value="modalForm.codeName" />
        </n-form-item>
      </n-form>
    </MeModal>
    <n-drawer v-model:show="dictDialog.show" width="40%">
      <n-drawer-content title="字典值" closable>
        <DictItemMgt :select-row="selectRow" />
      </n-drawer-content>
    </n-drawer>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { CommonPage } from '@/components/index.js'
import { useCrud } from '@/composables'
import DictItemMgt from '@/views/pms/dict/dictItem.vue'
import { NButton, NTag } from 'naive-ui'
import dictApi from './dictApi'

defineOptions({ name: 'DictMgt' })

const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})

/* 字典项 */
const selectRow = ref({})
const dictDialog = ref({
  show: false,
})
function handleDict(row) {
  selectRow.value = row
  dictDialog.value.show = true
}
/* 字典项 END */

onMounted(() => {
  $table.value?.handleSearch()
})

const { modalRef, modalFormRef, modalForm, handleAdd, handleDelete, handleEdit }
  = useCrud({
    name: '字典',
    doCreate: dictApi.create,
    doDelete: dictApi.delete,
    doUpdate: dictApi.update,
    initForm: { enable: true },
    refresh: (_, keepCurrentPage) => $table.value?.handleSearch(keepCurrentPage),
  })

const columns = [
  {
    title: '字典编码',
    key: 'code',
    render(row) {
      return [
        h(
          NTag,
          {
            size: 'small',
            type: 'primary',
            style: 'margin-left: 12px;',
            onClick: () => handleDict(row),
          },
          {
            default: () => row.code,
          },
        ),
      ]
    },
  },
  {
    title: '字典名称',
    key: 'codeName',
  },
  {
    title: '创建时间',
    key: 'createTime',
  },
  {
    title: '操作',
    key: 'actions',
    width: 320,
    align: 'right',
    fixed: 'right',
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            style: 'margin-left: 12px;',
            onClick: () => handleEdit(row),
          },
          {
            default: () => '编辑',
            icon: () => h('i', { class: 'i-material-symbols:edit-outline text-14' }),
          },
        ),

        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            disabled: row.code === 'SUPER_ADMIN',
            onClick: () => handleDelete(row.id),
          },
          {
            default: () => '删除',
            icon: () => h('i', { class: 'i-material-symbols:delete-outline text-14' }),
          },
        ),
      ]
    },
  },
]
</script>
