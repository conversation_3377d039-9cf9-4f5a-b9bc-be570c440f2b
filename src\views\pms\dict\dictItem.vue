<template>
  <CommonPage :show-header="false">
    <NButton type="primary" size="small" @click="handleAdd()">
      <i class="i-material-symbols:add mr-4 text-18" />
      新增字典项
    </NButton>
    <MeCrud
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="dictItemApi.pageList"
    />
    <MeModal ref="modalRef" width="520px">
      <n-form
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="80"
        :model="modalForm"
      >
        <n-form-item
          label="字典值"
          path="value"
          :rule="{
            required: true,
            message: '请输入字典值',
            trigger: ['input', 'blur'],
          }"
        >
          <n-input v-model:value="modalForm.value" />
        </n-form-item>
        <n-form-item
          label="标签名"
          path="label"
          :rule="{
            required: true,
            message: '请输入标签名',
            trigger: ['input', 'blur'],
          }"
        >
          <n-input v-model:value="modalForm.label" />
        </n-form-item>
        <n-form-item
          label="排序"
          path="sort"
          :rule="{
            type: 'number',
            required: true,
            message: '请输入排序',
            trigger: ['input', 'blur'],
          }"
        >
          <n-input-number v-model:value="modalForm.sort" />
        </n-form-item>
        <n-form-item
          label="备注"
          path="remarks"
          :rule="{
            required: false,
            message: '请输入备注',
            trigger: ['input', 'blur'],
          }"
        >
          <n-input v-model:value="modalForm.remarks" />
        </n-form-item>
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal } from '@/components'
import { CommonPage } from '@/components/index.js'
import { useCrud } from '@/composables'
import { NButton } from 'naive-ui'
import dictItemApi from './dictItemApi'

defineOptions({ name: 'DictItemMgt' })
const props = defineProps({
  selectRow: {
    type: Object,
    required: true,
  },
})
const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({
  dictId: props.selectRow.id,
})

const { modalRef, modalFormRef, modalForm, handleAdd, handleDelete, handleEdit }
  = useCrud({
    name: '字典项',
    doCreate: dictItemApi.create,
    doDelete: dictItemApi.delete,
    doUpdate: dictItemApi.update,
    initForm: {
      enable: true,
      dictId: props.selectRow.id,
    },
    refresh: (_, keepCurrentPage) => $table.value?.handleSearch(keepCurrentPage),
  })
onMounted(() => {
  $table.value?.handleSearch()
})
const columns = [
  { title: '字典值', key: 'value', width: 100 },
  { title: '标签名', key: 'label', width: 200 },
  { title: '排序', key: 'sort', width: 100 },
  { title: '备注', key: 'remarks', width: 200 },
  {
    title: '操作',
    key: 'actions',
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            onClick: () => handleEdit(row),
          },
          {
            default: () => '编辑',
          },
        ),

        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 6px;',
            onClick: () => handleDelete(row.id),
          },
          {
            default: () => '删除',
          },
        ),
      ]
    },
  },
]
</script>
