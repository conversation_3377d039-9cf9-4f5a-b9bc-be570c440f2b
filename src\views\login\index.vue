<!--------------------------------
 - @Author: <PERSON>
 - @LastEditor: <PERSON>
 - @LastEditTime: 2023/12/05 21:28:36
 - @Email: <EMAIL>
 - Copyright © 2023 <PERSON>(大脸怪) | https://isme.top
 --------------------------------->

<template>
  <div class="ocean-background">
    <!-- 海洋背景装饰 -->
    <div class="water-background" />
    <div class="floating-bubbles">
      <div class="bubble-float bubble-float-1" />
      <div class="bubble-float bubble-float-2" />
      <div class="bubble-float bubble-float-3" />
      <div class="bubble-float bubble-float-4" />
      <div class="bubble-float bubble-float-5" />
      <div class="bubble-float bubble-float-6" />
      <div class="bubble-float bubble-float-7" />
      <div class="bubble-float bubble-float-8" />
    </div>
    <div class="wave wave1" />
    <div class="wave wave2" />
    <div class="wave wave3" />
    <div class="wave wave4" />

    <!-- 海洋生物 -->
    <div class="sea-life">
      <!-- 小鱼 -->
      <div class="fish fish-1">
        <div class="fish-body" />
        <div class="fish-tail" />
      </div>
      <div class="fish fish-2">
        <div class="fish-body" />
        <div class="fish-tail" />
      </div>

      <!-- 水母 -->
      <div class="jellyfish">
        <div class="jellyfish-body" />
        <div class="jellyfish-tentacles">
          <div class="tentacle tentacle-1" />
          <div class="tentacle tentacle-2" />
          <div class="tentacle tentacle-3" />
          <div class="tentacle tentacle-4" />
        </div>
      </div>

      <!-- 水草 -->
      <div class="seaweed-container">
        <div class="seaweed seaweed-1" />
        <div class="seaweed seaweed-2" />
        <div class="seaweed seaweed-3" />
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="login-container">
      <div class="water-ripple" />

      <div class="login-form">
        <h2 class="title f-c-c text-24 text-#6a6a6a font-normal">
          <div class="title-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
              <circle cx="12" cy="7" r="4" />
            </svg>
          </div>
          {{ title }}
        </h2>

        <n-input
          v-model:value="loginInfo.username"
          autofocus
          class="mt-32 h-40 items-center"
          placeholder="请输入用户名"
          :maxlength="20"
        >
          <template #prefix>
            <i class="i-fe:user mr-12 opacity-20" />
          </template>
        </n-input>

        <n-input
          v-model:value="loginInfo.password"
          class="mt-20 h-40 items-center"
          type="password"
          show-password-on="mousedown"
          placeholder="请输入密码"
          :maxlength="20"
          @keydown.enter="handleLogin()"
        >
          <template #prefix>
            <i class="i-fe:lock mr-12 opacity-20" />
          </template>
        </n-input>

        <div class="mt-20 flex items-center">
          <n-input
            v-model:value="loginInfo.verificationCode"
            class="h-40 items-center"
            placeholder="请输入验证码"
            :maxlength="4"
            @keydown.enter="handleLogin()"
          >
            <template #prefix>
              <i class="i-fe:key mr-12 opacity-20" />
            </template>
          </n-input>
          <img
            v-if="captchaUrl"
            :src="captchaUrl"
            alt="验证码"
            height="40"
            class="ml-12 w-80 cursor-pointer rounded-5"
            @click="initCaptcha"
          >
        </div>

        <n-checkbox
          class="mt-20"
          :checked="isRemember"
          label="记住我"
          :on-update:checked="(val) => (isRemember = val)"
        />

        <div class="ocean-buttons mt-20 flex items-center">
          <n-button
            class="ocean-button ocean-button-primary h-40 flex-1 rounded-5 text-16"
            type="primary"
            ghost
            @click="receiveMessages()"
          >
            <span class="button-text">短信验证码</span>
            <div class="ocean-wave-effect" />
            <div class="water-drops">
              <span class="drop drop-1" />
              <span class="drop drop-2" />
              <span class="drop drop-3" />
            </div>
          </n-button>
          <n-button
            class="ocean-button ocean-button-primary ml-32 h-40 flex-1 rounded-5 text-16"
            type="primary"
            :loading="loading"
            @click="handleLogin()"
          >
            <span class="button-text">登录</span>
            <div class="ocean-wave-effect" />
            <div class="water-drops">
              <span class="drop drop-1" />
              <span class="drop drop-2" />
              <span class="drop drop-3" />
            </div>
          </n-button>
        </div>
      </div>
    </div>

    <TheFooter class="footer" />
  </div>
</template>

<script setup>
import { TheFooter } from '@/components/index.js'
import { useAuthStore } from '@/store'
import { lStorage, throttle } from '@/utils'
import { useStorage } from '@vueuse/core'
import api from './api'

const authStore = useAuthStore()
const router = useRouter()
const route = useRoute()
const title = import.meta.env.VITE_TITLE

const loginInfo = ref({
  username: '',
  password: '',
  verificationCodeId: '',
})

function receiveMessages() {
  router.push('/sms')
}

function generateUUID() {
  loginInfo.value.verificationCodeId = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

generateUUID()

const captchaUrl = ref('')
const initCaptcha = throttle(() => {
  captchaUrl.value = `${import.meta.env.VITE_AXIOS_BASE_URL}/auth/getCaptcha?verificationCodeId=${loginInfo.value.verificationCodeId}&time=${Date.now()}`
}, 500)

const localLoginInfo = lStorage.get('loginInfo')
if (localLoginInfo) {
  loginInfo.value.username = localLoginInfo.username || ''
  loginInfo.value.password = localLoginInfo.password || ''
}
initCaptcha()

const isRemember = useStorage('isRemember', true)
const loading = ref(false)

async function handleLogin(isQuick) {
  const { username, password, verificationCodeId, verificationCode } = loginInfo.value
  if (!username || !password)
    return $message.warning('请输入用户名和密码')
  if (!isQuick && !verificationCode)
    return $message.warning('请输入验证码')
  try {
    loading.value = true
    $message.loading('正在验证，请稍后...', { key: 'login' })
    const { data } = await api.login({ username, password: password.toString(), verificationCodeId, verificationCode, isQuick })
    if (isRemember.value) {
      lStorage.set('loginInfo', { username, password })
    }
    else {
      lStorage.remove('loginInfo')
    }
    await onLoginSuccess(data)
  }
  catch (error) {
    // 10003为验证码错误专属业务码
    if (error?.code === 10003) {
      // 为防止爆破，验证码错误则刷新验证码
      initCaptcha()
    }
    $message.destroy('login')
    console.error(error)
  }
  loading.value = false
}

async function onLoginSuccess(data = {}) {
  authStore.setToken({ accessToken: data.accessToken })
  $message.loading('登录中...', { key: 'login' })
  try {
    $message.success('登录成功', { key: 'login' })
    if (route.query.redirect) {
      const path = route.query.redirect
      delete route.query.redirect
      router.push({ path, query: route.query })
    }
    else {
      router.push('/')
    }
  }
  catch (error) {
    console.error(error)
    $message.destroy('login')
  }
}
</script>

<style scoped>
/* 海洋背景 */
.ocean-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(to bottom, #e0f7fa, #4fc3f7, #0288d1, #01579b);
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 波浪背景 */
.water-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.3) 0%, transparent 40%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.2) 0%, transparent 30%),
    radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.2) 0%, transparent 40%),
    radial-gradient(circle at 60% 60%, rgba(3, 169, 244, 0.3) 0%, transparent 50%);
  opacity: 0.8;
  z-index: 1;
}

/* 海洋生物 */
.sea-life {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* 小鱼 */
.fish {
  position: absolute;
  width: 40px;
  height: 20px;
  z-index: 3;
}

.fish-1 {
  top: 30%;
  left: 15%;
  animation: swim-right 25s ease-in-out infinite;
}

.fish-2 {
  top: 60%;
  right: 10%;
  animation: swim-left 30s ease-in-out infinite;
  transform: scaleX(-1);
}

.fish-body {
  position: absolute;
  width: 30px;
  height: 15px;
  background: linear-gradient(to right, #29b6f6, #4fc3f7);
  border-radius: 50% 70% 70% 50%;
}

.fish-tail {
  position: absolute;
  right: -10px;
  top: 0;
  width: 15px;
  height: 15px;
  background: #29b6f6;
  clip-path: polygon(0 0, 0 100%, 100% 50%);
  animation: tail-wiggle 0.6s ease-in-out infinite;
  transform-origin: left;
}

@keyframes tail-wiggle {
  0%,
  100% {
    transform: rotate(-5deg);
  }
  50% {
    transform: rotate(5deg);
  }
}

@keyframes swim-right {
  0%,
  100% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(20vw, 10vh);
  }
  50% {
    transform: translate(70vw, -5vh);
  }
  75% {
    transform: translate(40vw, 8vh);
  }
}

@keyframes swim-left {
  0%,
  100% {
    transform: scaleX(-1) translate(0, 0);
  }
  25% {
    transform: scaleX(-1) translate(-30vw, 5vh);
  }
  50% {
    transform: scaleX(-1) translate(-60vw, -10vh);
  }
  75% {
    transform: scaleX(-1) translate(-20vw, 15vh);
  }
}

/* 水母 */
.jellyfish {
  position: absolute;
  top: 40%;
  right: 25%;
  width: 30px;
  height: 50px;
  animation: jellyfish-float 18s ease-in-out infinite;
  z-index: 3;
}

.jellyfish-body {
  position: absolute;
  width: 30px;
  height: 30px;
  background: rgba(144, 202, 249, 0.7);
  border-radius: 50% 50% 30% 30%;
  animation: jellyfish-pulse 2s ease-in-out infinite;
}

.jellyfish-tentacles {
  position: absolute;
  top: 25px;
  left: 0;
  width: 30px;
  height: 25px;
  display: flex;
  justify-content: space-around;
}

.tentacle {
  width: 2px;
  height: 100%;
  background: rgba(144, 202, 249, 0.5);
  animation: tentacle-wiggle 3s ease-in-out infinite;
  transform-origin: top;
}

.tentacle-1 {
  animation-delay: 0s;
}

.tentacle-2 {
  animation-delay: 0.5s;
}

.tentacle-3 {
  animation-delay: 1s;
}

.tentacle-4 {
  animation-delay: 1.5s;
}

@keyframes jellyfish-pulse {
  0%,
  100% {
    transform: scaleY(1);
  }
  50% {
    transform: scaleY(0.8);
  }
}

@keyframes tentacle-wiggle {
  0%,
  100% {
    transform: rotate(-5deg);
  }
  50% {
    transform: rotate(5deg);
  }
}

@keyframes jellyfish-float {
  0%,
  100% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(-10vw, -10vh);
  }
  50% {
    transform: translate(-5vw, 5vh);
  }
  75% {
    transform: translate(5vw, -5vh);
  }
}

/* 水草 */
.seaweed-container {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100px;
  display: flex;
  justify-content: space-between;
  padding: 0 10%;
  z-index: 2;
}

.seaweed {
  width: 8px;
  background: linear-gradient(to top, #26a69a, #4db6ac, #80cbc4);
  border-radius: 5px;
  transform-origin: bottom;
}

.seaweed-1 {
  height: 70px;
  animation: seaweed-sway 5s ease-in-out infinite;
}

.seaweed-2 {
  height: 100px;
  animation: seaweed-sway 6s ease-in-out infinite;
  animation-delay: 1s;
}

.seaweed-3 {
  height: 85px;
  animation: seaweed-sway 4s ease-in-out infinite;
  animation-delay: 0.5s;
}

@keyframes seaweed-sway {
  0%,
  100% {
    transform: rotate(-8deg);
  }
  50% {
    transform: rotate(8deg);
  }
}

/* 波浪动画效果 */
.wave {
  position: absolute;
  width: 200%;
  height: 200px;
  border-radius: 48%;
  background: rgba(255, 255, 255, 0.2);
  animation: wave 15s linear infinite;
}

.wave1 {
  bottom: 10%;
  opacity: 0.2;
  animation-duration: 15s;
}

.wave2 {
  bottom: 15%;
  opacity: 0.3;
  animation-duration: 17s;
  animation-delay: 1s;
}

.wave3 {
  bottom: 20%;
  opacity: 0.1;
  animation-duration: 20s;
  animation-delay: 2s;
}

.wave4 {
  bottom: 25%;
  opacity: 0.2;
  animation-duration: 22s;
  animation-delay: 3s;
}

@keyframes wave {
  0% {
    transform: translate(-50%, 0) rotate(0deg);
  }
  100% {
    transform: translate(-50%, 0) rotate(360deg);
  }
}

/* 漂浮气泡 */
.floating-bubbles {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.bubble-float {
  position: absolute;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.1) 60%,
    transparent 70%
  );
  border-radius: 50%;
  animation: float-up 15s ease-in-out infinite;
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
  z-index: 2;
}

.bubble-float-1 {
  width: 50px;
  height: 50px;
  left: 10%;
  bottom: -50px;
  animation-duration: 15s;
  animation-delay: 0s;
}

.bubble-float-2 {
  width: 30px;
  height: 30px;
  left: 20%;
  bottom: -30px;
  animation-duration: 18s;
  animation-delay: 1s;
}

.bubble-float-3 {
  width: 25px;
  height: 25px;
  left: 35%;
  bottom: -25px;
  animation-duration: 12s;
  animation-delay: 2s;
}

.bubble-float-4 {
  width: 40px;
  height: 40px;
  left: 50%;
  bottom: -40px;
  animation-duration: 16s;
  animation-delay: 3s;
}

.bubble-float-5 {
  width: 35px;
  height: 35px;
  left: 65%;
  bottom: -35px;
  animation-duration: 14s;
  animation-delay: 4s;
}

.bubble-float-6 {
  width: 20px;
  height: 20px;
  left: 80%;
  bottom: -20px;
  animation-duration: 20s;
  animation-delay: 5s;
}

.bubble-float-7 {
  width: 45px;
  height: 45px;
  left: 90%;
  bottom: -45px;
  animation-duration: 17s;
  animation-delay: 6s;
}

.bubble-float-8 {
  width: 25px;
  height: 25px;
  left: 5%;
  bottom: -25px;
  animation-duration: 19s;
  animation-delay: 7s;
}

@keyframes float-up {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-50vh) rotate(180deg);
    opacity: 0.8;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

/* 登录容器 - 扁平化风格 */
.login-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 16px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  position: relative;
  z-index: 10;
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.login-container:hover {
  background: rgba(255, 255, 255, 0.18);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 水波纹效果 */
.water-ripple {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  background: radial-gradient(circle at 30% 50%, rgba(255, 255, 255, 0.08) 0%, transparent 20%),
    radial-gradient(circle at 70% 20%, rgba(255, 255, 255, 0.06) 0%, transparent 15%),
    radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 30%);
  z-index: 1;
}

/* 登录表单 */
.login-form {
  padding: 48px 32px 32px 32px;
  position: relative;
  z-index: 2;
}

.title {
  margin-bottom: 32px;
  color: #37474f;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.title-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: rgba(0, 172, 193, 0.1);
  border-radius: 50%;
  color: #00acc1;
}

/* 页脚 */
.footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 15;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

/* 添加光线效果 */
.ocean-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 30%;
  width: 40%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(15deg);
  z-index: 1;
  pointer-events: none;
}

/* 生成更多小气泡 */
@keyframes micro-bubbles {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    transform: translateY(-100px) scale(0.5);
    opacity: 0;
  }
}

.login-container::after {
  content: '';
  position: absolute;
  bottom: -20px;
  right: 20px;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: white;
  box-shadow:
    15px -40px 0 1px rgba(255, 255, 255, 0.6),
    30px -80px 0 0px rgba(255, 255, 255, 0.5),
    45px -30px 0 2px rgba(255, 255, 255, 0.7),
    60px -60px 0 1px rgba(255, 255, 255, 0.4),
    75px -20px 0 1px rgba(255, 255, 255, 0.6),
    90px -50px 0 1px rgba(255, 255, 255, 0.5);
  animation: micro-bubbles 15s linear infinite;
  z-index: 1;
}

.login-container::before {
  content: '';
  position: absolute;
  bottom: -20px;
  left: 20px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: white;
  box-shadow:
    15px -30px 0 1px rgba(255, 255, 255, 0.5),
    30px -70px 0 0px rgba(255, 255, 255, 0.4),
    45px -40px 0 2px rgba(255, 255, 255, 0.6),
    60px -20px 0 1px rgba(255, 255, 255, 0.5),
    75px -50px 0 1px rgba(255, 255, 255, 0.4),
    90px -10px 0 0px rgba(255, 255, 255, 0.5);
  animation: micro-bubbles 12s linear infinite;
  animation-delay: 2s;
  z-index: 1;
}

/* 海洋风格按钮 */
.ocean-buttons {
  position: relative;
}

.ocean-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border: none !important;
  z-index: 1;
}

.ocean-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
  z-index: 2;
}

.ocean-button:hover::before {
  left: 100%;
}

.ocean-button-ghost {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.8)) !important;
  border: 2px solid #00acc1 !important;
  color: #006064 !important;
  box-shadow:
    0 4px 15px rgba(0, 172, 193, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  font-weight: 600 !important;
}

.ocean-button-ghost:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(240, 248, 255, 0.9)) !important;
  border-color: #00bcd4 !important;
  color: #004d5c !important;
  box-shadow:
    0 6px 20px rgba(0, 172, 193, 0.35),
    inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
  transform: translateY(-1px);
}

.ocean-button-primary {
  background: linear-gradient(135deg, #00acc1, #0288d1) !important;
  color: white !important;
  box-shadow:
    0 4px 15px rgba(0, 172, 193, 0.35),
    inset 0 1px 0 rgba(255, 255, 255, 0.25) !important;
  font-weight: 600 !important;
}

.ocean-button-primary:hover {
  background: linear-gradient(135deg, #00bcd4, #03a9f4) !important;
  color: white !important;
  box-shadow:
    0 6px 20px rgba(0, 172, 193, 0.45),
    inset 0 1px 0 rgba(255, 255, 255, 0.35) !important;
  transform: translateY(-1px);
}

.button-text {
  position: relative;
  z-index: 3;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.5px;
}

.ocean-button-ghost .button-text {
  color: #004d5c !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.ocean-button-primary .button-text {
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 海洋波浪效果 */
.ocean-wave-effect {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120"><path d="M0,60 C300,110 900,10 1200,60 L1200,120 L0,120 Z" fill="rgba(255,255,255,0.15)"/></svg>');
  background-size: 200% 100%;
  animation: wave-flow 3s ease-in-out infinite;
  z-index: 1;
}

.ocean-button-ghost .ocean-wave-effect {
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120"><path d="M0,60 C300,110 900,10 1200,60 L1200,120 L0,120 Z" fill="rgba(0,172,193,0.2)"/></svg>');
  background-size: 200% 100%;
}

@keyframes wave-flow {
  0%,
  100% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 100% 0%;
  }
}

/* 水珠效果 */
.water-drops {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

.drop {
  position: absolute;
  width: 3px;
  height: 3px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  animation: drop-float 4s ease-in-out infinite;
  opacity: 0;
}

.ocean-button-ghost .drop {
  background: rgba(0, 172, 193, 0.3);
}

.drop-1 {
  left: 20%;
  animation-delay: 0s;
}

.drop-2 {
  left: 50%;
  animation-delay: 1.3s;
}

.drop-3 {
  left: 80%;
  animation-delay: 2.6s;
}

@keyframes drop-float {
  0%,
  100% {
    opacity: 0;
    transform: translateY(0);
  }
  10% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
    transform: translateY(-10px);
  }
  90% {
    opacity: 0.2;
  }
}

.ocean-button:hover .drop {
  animation-duration: 2s;
}

.ocean-button:hover .ocean-wave-effect {
  animation-duration: 1.5s;
}

/* 按钮点击水波纹效果 */
.ocean-button:active::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  animation: ripple-effect 0.6s ease-out;
  z-index: 2;
}

@keyframes ripple-effect {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 100px;
    height: 100px;
    opacity: 0;
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .login-container {
    max-width: 90%;
    margin: 20px auto;
  }

  .login-form {
    padding: 40px 24px 24px 24px;
  }

  .mt-20 .flex {
    flex-direction: column;
  }

  .mt-20 .flex .ml-32 {
    margin-left: 0;
    margin-top: 16px;
  }

  .ocean-button {
    height: 44px !important;
  }
}
</style>
