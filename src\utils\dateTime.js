/**
 * 日期时间工具函数
 */

import { formatDateTime as formatDateTimeFromCommon } from './common'

// formatDateTime is exported from common.js to avoid conflicts

/**
 * 格式化日期为 yyyy-MM-dd 格式
 * @param {Date|string|number} date - 日期对象、日期字符串或时间戳
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date) {
  if (!date)
    return ''

  const d = new Date(date)
  if (isNaN(d.getTime()))
    return ''

  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

/**
 * 格式化时间为 HH:mm:ss 格式
 * @param {Date|string|number} date - 日期对象、日期字符串或时间戳
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(date) {
  if (!date)
    return ''

  const d = new Date(date)
  if (isNaN(d.getTime()))
    return ''

  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')

  return `${hours}:${minutes}:${seconds}`
}

/**
 * 格式化日期时间为指定格式
 * @param {Date|string|number} date - 日期对象、日期字符串或时间戳
 * @param {string} format - 格式字符串，支持 YYYY、MM、DD、HH、mm、ss
 * @returns {string} 格式化后的日期时间字符串
 */
export function formatDateTimeCustom(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date)
    return ''

  const d = new Date(date)
  if (isNaN(d.getTime()))
    return ''

  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')

  return format
    .replace(/YYYY/g, year)
    .replace(/MM/g, month)
    .replace(/DD/g, day)
    .replace(/HH/g, hours)
    .replace(/mm/g, minutes)
    .replace(/ss/g, seconds)
}

/**
 * 解析日期字符串为Date对象
 * @param {string} dateString - 日期字符串
 * @returns {Date|null} Date对象或null
 */
export function parseDate(dateString) {
  if (!dateString)
    return null

  const date = new Date(dateString)
  return isNaN(date.getTime()) ? null : date
}

/**
 * 获取当前日期时间字符串
 * @param {string} format - 格式字符串，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 当前日期时间字符串
 */
export function getCurrentDateTime(format = 'YYYY-MM-DD HH:mm:ss') {
  return formatDateTimeCustom(new Date(), format)
}

/**
 * 获取当前日期字符串
 * @returns {string} 当前日期字符串 (YYYY-MM-DD)
 */
export function getCurrentDate() {
  return formatDate(new Date())
}

/**
 * 获取当前时间字符串
 * @returns {string} 当前时间字符串 (HH:mm:ss)
 */
export function getCurrentTime() {
  return formatTime(new Date())
}

/**
 * 计算两个日期之间的天数差
 * @param {Date|string|number} startDate - 开始日期
 * @param {Date|string|number} endDate - 结束日期
 * @returns {number} 天数差
 */
export function getDaysDiff(startDate, endDate) {
  const start = new Date(startDate)
  const end = new Date(endDate)

  if (isNaN(start.getTime()) || isNaN(end.getTime()))
    return 0

  const diffTime = Math.abs(end - start)
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

/**
 * 判断是否为今天
 * @param {Date|string|number} date - 日期
 * @returns {boolean} 是否为今天
 */
export function isToday(date) {
  const d = new Date(date)
  const today = new Date()

  return d.getFullYear() === today.getFullYear()
    && d.getMonth() === today.getMonth()
    && d.getDate() === today.getDate()
}

/**
 * 获取相对时间描述（如：刚刚、1分钟前、1小时前等）
 * @param {Date|string|number} date - 日期
 * @returns {string} 相对时间描述
 */
export function getRelativeTime(date) {
  const d = new Date(date)
  const now = new Date()
  const diff = now - d

  if (diff < 0)
    return '未来时间'

  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (seconds < 60)
    return '刚刚'
  if (minutes < 60)
    return `${minutes}分钟前`
  if (hours < 24)
    return `${hours}小时前`
  if (days < 7)
    return `${days}天前`

  return formatDate(d)
}

/**
 * 时间范围格式化为后端需要的格式
 * @param {Array} timeRange - 时间范围数组 [startTime, endTime]
 * @returns {object} 格式化后的时间对象 {startTime, endTime}
 */
export function formatTimeRange(timeRange) {
  if (!timeRange || timeRange.length !== 2) {
    return { startTime: null, endTime: null }
  }

  return {
    startTime: formatDateTimeFromCommon(new Date(timeRange[0])),
    endTime: formatDateTimeFromCommon(new Date(timeRange[1])),
  }
}
