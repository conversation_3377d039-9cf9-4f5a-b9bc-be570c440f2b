<template>
  <div class="flex-col-stretch min-h-500px gap-16px overflow-hidden lt-sm:overflow-auto">
    <!-- 查询条件 -->
    <n-card title="登录日志" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <n-space>
          <NButton type="primary" ghost @click="handleSearch">
            <i class="i-material-symbols:search mr-4 text-16" />
            搜索
          </NButton>
          <NButton @click="handleReset">
            <i class="i-material-symbols:refresh mr-4 text-16" />
            重置
          </NButton>
          <NButton type="error" ghost @click="handleClearAll">
            <i class="i-material-symbols:delete-forever mr-4 text-16" />
            清空日志
          </NButton>
        </n-space>
      </template>

      <!-- 搜索表单 -->
      <n-form ref="queryFormRef" :model="queryParams" label-placement="left" :label-width="80">
        <n-grid :cols="24" :x-gap="18">
          <n-form-item-gi :span="6" label="用户名" path="username">
            <n-input v-model:value="queryParams.username" placeholder="请输入用户名" clearable />
          </n-form-item-gi>
          <n-form-item-gi :span="6" label="IP地址" path="ipAddress">
            <n-input v-model:value="queryParams.ipAddress" placeholder="请输入IP地址" clearable />
          </n-form-item-gi>
          <n-form-item-gi :span="6" label="登录状态" path="loginStatus">
            <n-select
              v-model:value="queryParams.loginStatus"
              placeholder="请选择登录状态"
              clearable
              :options="loginStatusOptions"
            />
          </n-form-item-gi>
          <n-form-item-gi :span="6" label="登录地点" path="location">
            <n-input v-model:value="queryParams.location" placeholder="请输入登录地点" clearable />
          </n-form-item-gi>
          <n-form-item-gi :span="12" label="登录时间" path="timeRange">
            <n-date-picker
              v-model:value="timeRange"
              type="datetimerange"
              clearable
              placeholder="请选择时间范围"
              class="w-full"
              @update:value="handleTimeRangeChange"
            />
          </n-form-item-gi>
        </n-grid>
      </n-form>

      <!-- 数据表格 -->
      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="row => row.id"
        flex-height
        class="sm:h-600px"
      />
    </n-card>
  </div>
</template>

<script setup>
import { formatTimeRange } from '@/utils'
import { NButton, NPopconfirm, NTag } from 'naive-ui'
import api from './api'

defineOptions({ name: 'LoginLog' })

// 查询参数
const queryParams = ref({
  username: '',
  ipAddress: '',
  loginStatus: null,
  location: '',
  startTime: null,
  endTime: null,
})

// 时间范围
const timeRange = ref(null)

// 登录状态选项
const loginStatusOptions = [
  { label: '成功', value: true },
  { label: '失败', value: false },
]

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  onChange: (page) => {
    pagination.page = page
    getTableData()
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    getTableData()
  },
})

// 表格列配置
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    render: (_, index) => {
      return (pagination.page - 1) * pagination.pageSize + index + 1
    },
  },
  {
    title: '用户名',
    key: 'username',
    width: 120,
  },
  {
    title: 'IP地址',
    key: 'ipAddress',
    width: 140,
  },
  {
    title: '登录地点',
    key: 'location',
    width: 120,
  },
  {
    title: '登录状态',
    key: 'loginStatus',
    width: 100,
    render: (row) => {
      return h(
        NTag,
        {
          type: row.loginStatus ? 'success' : 'error',
          size: 'small',
        },
        {
          default: () => (row.loginStatus ? '成功' : '失败'),
        },
      )
    },
  },
  {
    title: '登录信息',
    key: 'loginMessage',
    width: 150,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '登录时间',
    key: 'loginTime',
    width: 180,
    render: (row) => {
      return row.loginTime ? new Date(row.loginTime).toLocaleString() : '-'
    },
  },
  {
    title: '用户代理',
    key: 'userAgent',
    width: 200,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '操作',
    key: 'actions',
    width: 100,
    fixed: 'right',
    render: (row) => {
      return h(
        NPopconfirm,
        {
          onPositiveClick: () => handleDelete(row.id),
        },
        {
          default: () => '确认删除这条登录日志吗？',
          trigger: () =>
            h(
              NButton,
              {
                size: 'small',
                type: 'error',
                ghost: true,
              },
              { default: () => '删除' },
            ),
        },
      )
    },
  },
]

// 获取表格数据
async function getTableData() {
  try {
    loading.value = true
    const params = {
      pageNumber: pagination.page,
      pageSize: pagination.pageSize,
      ...queryParams.value,
    }
    const { data } = await api.getPageList(params)
    tableData.value = data.records || []
    pagination.itemCount = data.total || 0
  }
  catch (error) {
    console.error('获取登录日志失败：', error)
    $message.error('获取登录日志失败')
  }
  finally {
    loading.value = false
  }
}

// 搜索
function handleSearch() {
  pagination.page = 1
  getTableData()
}

// 重置
function handleReset() {
  queryParams.value = {
    username: '',
    ipAddress: '',
    loginStatus: null,
    location: '',
    startTime: null,
    endTime: null,
  }
  timeRange.value = null
  pagination.page = 1
  getTableData()
}

// 时间范围变化
function handleTimeRangeChange(value) {
  const timeRange = formatTimeRange(value)
  queryParams.value.startTime = timeRange.startTime
  queryParams.value.endTime = timeRange.endTime
}

// 删除单条记录
async function handleDelete(id) {
  try {
    await api.deleteById(id)
    $message.success('删除成功')
    getTableData()
  }
  catch (error) {
    console.error('删除失败：', error)
    $message.error('删除失败')
  }
}

// 清空所有日志
function handleClearAll() {
  $dialog.warning({
    title: '警告',
    content: '确认要清空所有登录日志吗？此操作不可恢复！',
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await api.clearAll()
        $message.success('清空成功')
        getTableData()
      }
      catch (error) {
        console.error('清空失败：', error)
        $message.error('清空失败')
      }
    },
  })
}

// 初始化
onMounted(() => {
  getTableData()
})
</script>

<style scoped>
.card-wrapper {
  height: 100%;
}
</style>
