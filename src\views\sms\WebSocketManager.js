/* eslint-disable no-console */
class WebSocketManager {
  constructor(url, heartbeatInterval = 30000) {
    this.url = url
    this.socket = null
    this.isConnected = false
    this.heartbeatInterval = heartbeatInterval
    this.heartbeatTimer = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
  }

  connect() {
    this.socket = new WebSocket(this.url)

    this.socket.onopen = () => {
      // console.log('WebSocket连接已建立');
      this.isConnected = true
      this.reconnectAttempts = 0
      this.startHeartbeat()
    }

    this.socket.onmessage = (event) => {
      // console.log('接收到消息:', event.data)
      try {
        const parsedMessage = JSON.parse(event.data)
        if (parsedMessage.type === 'heartbeat') {
          // console.log('收到心跳响应');
          return
        }
        this.handleMessage(parsedMessage)
      }
      catch (error) {
        console.log('未定义的消息', error)
      }
    }

    // eslint-disable-next-line unused-imports/no-unused-vars
    this.socket.onclose = (event) => {
      this.isConnected = false
      this.stopHeartbeat()
      this.reconnect()
    }

    // eslint-disable-next-line node/handle-callback-err,unused-imports/no-unused-vars
    this.socket.onerror = (error) => {
      // console.error('WebSocket发生错误:', error);
      this.stopHeartbeat()
    }
  }

  startHeartbeat() {
    this.stopHeartbeat()
    this.heartbeatTimer = window.setInterval(() => {
      if (this.isConnected) {
        const heartbeatMessage = {
          type: 'ping',
        }
        // console.log('发送心跳消息');
        this.sendMessage(heartbeatMessage)
      }
    }, this.heartbeatInterval)
  }

  stopHeartbeat() {
    if (this.heartbeatTimer) {
      // console.log('停止心跳')
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      const delay = 2 ** this.reconnectAttempts * 1000
      // console.log(`正在尝试重新连接 (尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      setTimeout(() => this.connect(), delay)
    }
    else {
      // console.error('达到最大重连次数，停止重试');
    }
  }

  // eslint-disable-next-line unused-imports/no-unused-vars
  handleMessage(message) {
    // console.log('处理消息:', message);
  }

  sendMessage(message) {
    if (this.isConnected && this.socket) {
      this.socket.send(JSON.stringify(message))
    }
    else {
      console.warn('WebSocket未连接')
    }
  }

  disconnect() {
    // console.log('关闭连接')
    this.stopHeartbeat()
    if (this.socket) {
      this.socket.close()
    }
  }
}

export { WebSocketManager }
