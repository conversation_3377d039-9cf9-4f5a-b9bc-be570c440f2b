import api from '@/api'
import { basePermissions } from '@/settings'

export async function getUserInfo() {
  const res = await api.getUser()
  const profile = res.data || {}
  return {
    id: profile.id,
    username: profile.username,
    avatar: profile?.avatar,
    nickname: profile?.nickname,
    gender: profile?.gender,
    address: profile?.address,
    email: profile?.email,
    roles: profile.roles,
    currentRole: profile.roles?.length ? profile.roles[0] : [],
  }
}

export async function getPermissions() {
  let asyncPermissions = []
  try {
    const res = await api.getRolePermissions()
    asyncPermissions = res?.data || []
  }
  catch (error) {
    console.error(error)
  }
  return basePermissions.concat(asyncPermissions)
}
