{"name": "vue-naive-admin", "type": "module", "version": "2.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "up": "taze major -I"}, "dependencies": {"@arco-design/color": "^0.4.0", "@vueuse/core": "^12.0.0", "axios": "^1.7.9", "dayjs": "^1.11.13", "echarts": "^5.5.1", "lodash-es": "^4.17.21", "naive-ui": "^2.40.3", "pinia": "^2.3.0", "pinia-plugin-persistedstate": "^4.1.3", "pnpm": "^9.15.0", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@iconify/json": "^2.2.282", "@unocss/preset-rem-to-px": "^0.65.1", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "esno": "^4.8.0", "fs-extra": "^11.2.0", "glob": "^11.0.0", "rollup-plugin-visualizer": "^5.12.0", "taze": "^0.18.0", "unocss": "^0.65.1", "unplugin-auto-import": "^0.19.0", "unplugin-vue-components": "^0.27.5", "vfonts": "^0.0.3", "vite": "^6.0.3", "vite-plugin-router-warn": "^1.0.0", "vite-plugin-vue-devtools": "^7.6.8", "vue3-intro-step": "^1.0.5"}}