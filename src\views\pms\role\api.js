/**********************************
 * @Author: <PERSON>
 * @LastEditor: <PERSON>
 * @LastEditTime: 2023/12/05 21:29:27
 * @Email: <EMAIL>
 * Copyright © 2023 <PERSON>(大脸怪) | https://isme.top
 **********************************/

import { request } from '@/utils'

export default {
  create: data => request.post('/role/add', data),
  read: (params = {}) => request.get('/role/list', { params }),
  update: data => request.post(`/role/update`, data),
  delete: id => request.post(`/role/deleteById/${id}`),

  getAllPermissionTree: () => request.get('/menu/all'),
  getAllUsers: (params = {}) => request.get('/user/list', { params }),
  addRoleUsers: (roleId, data) => request.post(`/user_role/batchInsert/${roleId}`, data),
  removeRoleUsers: (roleId, data) => request.post(`/user_role/batchDelete/${roleId}`, data),
}
