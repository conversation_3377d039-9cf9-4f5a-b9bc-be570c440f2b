{"themeName": "VibrantGlassmorphism", "description": "A UI design system based on the Glassmorphism style, featuring frosted glass effects, vibrant gradient backgrounds, floating elements, and clean typography. Inspired by modern app interfaces.", "globalStyles": {"palette": {"primaryGradients": ["linear-gradient(120deg, #ff81ae, #a269ff, #69c5ff)", "radial-gradient(circle at top left, #ffc700, #ff81ae)", "linear-gradient(45deg, #f3ec78, #af4261)", "linear-gradient(135deg, #667eea, #764ba2)"], "backgroundColors": ["#f0f2f5", "#1a1a1a"], "textColorPrimary": "#ffffff", "textColorSecondary": "rgba(255, 255, 255, 0.7)", "textColorAccent": "#000000", "iconColor": "#ffffff"}, "typography": {"fontFamily": "'SF Pro Display', 'Helvetica Neue', 'Arial', sans-serif", "largeTitle": {"fontSize": "72px", "fontWeight": "bold"}, "title1": {"fontSize": "28px", "fontWeight": "bold"}, "headline": {"fontSize": "18px", "fontWeight": "600"}, "body": {"fontSize": "16px", "fontWeight": "400"}, "caption": {"fontSize": "12px", "fontWeight": "500"}}, "effects": {"glassPanel": {"backgroundColor": "rgba(255, 255, 255, 0.15)", "backdropFilter": "blur(40px)", "border": "1.5px solid rgba(255, 255, 255, 0.2)", "borderRadius": "24px", "boxShadow": "0 8px 32px 0 rgba(31, 38, 135, 0.15)"}}, "spacing": {"base": "8px", "padding": "24px", "gap": "16px"}}, "components": [{"name": "MusicPlayerCard", "style": {"extends": "effects.glassPanel", "width": "300px", "padding": "20px", "display": "flex", "flexDirection": "column", "alignItems": "center", "gap": "20px"}, "elements": [{"type": "CircularPlayer", "style": {"width": "120px", "height": "120px", "borderRadius": "50%", "backgroundColor": "rgba(255, 255, 255, 0.2)", "display": "flex", "justifyContent": "center", "alignItems": "center"}, "children": [{"type": "PlayButton", "style": {"width": "60px", "height": "60px", "borderRadius": "50%", "backgroundColor": "#ff81ae", "color": "white"}}]}, {"type": "TextBlock", "content": "PLAYING", "style": "typography.headline", "color": "textColorPrimary"}, {"type": "TextBlock", "content": "Music Artist", "style": "typography.body", "color": "textColorSecondary"}]}, {"name": "FeatureHighlightCard", "style": {"display": "flex", "justifyContent": "center", "alignItems": "center", "position": "relative", "height": "400px"}, "elements": [{"type": "GlassCircle", "style": {"extends": "effects.glassPanel", "width": "320px", "height": "320px", "borderRadius": "50%", "display": "flex", "flexDirection": "column", "justifyContent": "center", "alignItems": "center"}, "children": [{"type": "TextBlock", "content": "01", "style": "typography.largeTitle", "color": "textColorAccent"}, {"type": "TextBlock", "content": "APP DESIGN", "style": "typography.headline", "color": "textColorAccent"}]}, {"type": "DecorativeBlob", "style": {"position": "absolute", "width": "200px", "height": "200px", "background": "linear-gradient(120deg, #ff81ae, #a269ff)", "borderRadius": "50%", "filter": "blur(50px)", "zIndex": "-1", "top": "10%", "left": "20%"}}]}, {"name": "InfoCard", "style": {"extends": "effects.glassPanel", "padding": "16px", "borderRadius": "16px", "display": "flex", "alignItems": "center", "gap": "12px"}, "elements": [{"type": "Number", "style": {"fontFamily": "typography.fontFamily", "fontSize": "32px", "fontWeight": "bold", "color": "textColorPrimary"}}, {"type": "TextBlock", "style": {"fontFamily": "typography.fontFamily", "fontSize": "14px", "fontWeight": "600", "color": "textColorPrimary"}}]}]}