/**********************************
 * @Author: <PERSON>
 * @LastEditor: <PERSON>
 * @LastEditTime: 2024/04/01 15:52:04
 * @Email: <EMAIL>
 * Copyright © 2023 <PERSON>(大脸怪) | https://isme.top
 **********************************/

import { request } from '@/utils'
import axios from 'axios'

export default {
  // getMenuTree: () => request.get('/permission/menu/tree'),
  getMenuTree: () => request.get('/menu/all'),
  // getButtons: ({ parentId }) => request.get(`/permission/button/${parentId}`), // 获取按钮
  getButtons: ({ parentId }) => request.get(`/menu/button/${parentId}`),
  getComponents: () => axios.get(`${import.meta.env.VITE_PUBLIC_PATH}components.json`),
  // addPermission: data => request.post('/permission', data), // 新增菜单
  addPermission: data => request.post('/menu/add', data),
  // savePermission: (id, data) => request.patch(`/permission/${id}`, data), // 编辑菜单
  savePermission: (id, data) => request.post(`/menu/update`, data),
  deletePermission: id => request.delete(`/menu/delete/${id}`),
}
