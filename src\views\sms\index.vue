<template>
  <div class="ocean-background">
    <!-- 海洋背景装饰 -->
    <div class="water-background" />
    <div class="floating-bubbles">
      <div class="bubble-float bubble-float-1" />
      <div class="bubble-float bubble-float-2" />
      <div class="bubble-float bubble-float-3" />
      <div class="bubble-float bubble-float-4" />
      <div class="bubble-float bubble-float-5" />
      <div class="bubble-float bubble-float-6" />
      <div class="bubble-float bubble-float-7" />
      <div class="bubble-float bubble-float-8" />
    </div>
    <div class="wave wave1" />
    <div class="wave wave2" />
    <div class="wave wave3" />
    <div class="wave wave4" />

    <!-- 海洋生物 -->
    <div class="sea-life">
      <!-- 小鱼 -->
      <div class="fish fish-1">
        <div class="fish-body" />
        <div class="fish-tail" />
      </div>
      <div class="fish fish-2">
        <div class="fish-body" />
        <div class="fish-tail" />
      </div>

      <!-- 水母 -->
      <div class="jellyfish">
        <div class="jellyfish-body" />
        <div class="jellyfish-tentacles">
          <div class="tentacle tentacle-1" />
          <div class="tentacle tentacle-2" />
          <div class="tentacle tentacle-3" />
          <div class="tentacle tentacle-4" />
        </div>
      </div>

      <!-- 水草 -->
      <div class="seaweed-container">
        <div class="seaweed seaweed-1" />
        <div class="seaweed seaweed-2" />
        <div class="seaweed seaweed-3" />
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="elegant-container">
      <div class="water-ripple" />
      <div class="header">
        <div class="water-wave" />
        <h1 class="title">
          短信验证码
          <span class="bubble" />
          <span class="bubble" />
          <span class="bubble" />
        </h1>
      </div>
      <div id="messages" class="message-scroll-area">
        <div class="raindrop raindrop-1" />
        <div class="raindrop raindrop-2" />
        <div class="raindrop raindrop-3" />
        <div class="card-container">
          <div
            v-for="(message, index) in messages"
            :key="message.timestamp || index"
            class="message-card"
            :class="{ 'droplet-animation': message.animated }"
            @animationend="message.animated = false"
          >
            <div class="message-content">
              <pre v-html="formatMessageContent(message.content)" />
            </div>
            <div
              v-if="extractVerificationCode(message.content)"
              class="copy-button"
              :class="{ copied: copiedStates[index] }"
              @click="copyVerificationCode(message.content, index)"
            >
              <span class="copy-icon">
                <svg v-if="!copiedStates[index]" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2" />
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" />
                </svg>
                <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M20 6L9 17l-5-5" />
                </svg>
              </span>
              <span class="copy-text">{{ copiedStates[index] ? '已复制' : '复制验证码' }}</span>
            </div>
          </div>
        </div>
      </div>
      <TheFooter class="py-12" />
    </div>
  </div>
</template>

<script setup>
import { TheFooter } from '@/components/index.js'
import api from '@/views/sms/sms.js'
import { onMounted, onUnmounted, ref } from 'vue'
import { WebSocketManager } from './WebSocketManager'

let wsManager

const messages = ref([])
const copiedStates = ref({})

/**
 * 从短信内容中提取验证码
 * @param {string} content 短信内容
 * @returns {string|null} 提取到的验证码，没有则返回null
 */
function extractVerificationCode(content) {
  if (!content)
    return null

  // 先尝试从常见的验证码格式中提取
  const commonPatterns = [
    /：\s*([a-z0-9]{4,8})/i,
    /验证码为[:：]?\s*([a-z0-9]{4,8})/i,
    /验证码是[:：]?\s*([a-z0-9]{4,8})/i,
    /验证码[为是:：]\s*([a-z0-9]{4,8})/i,
    /码[为是:：]\s*([a-z0-9]{4,8})/i,
    /code[: ]\s*([a-z0-9]{4,8})/i,
    /动态码为[:：]?\s*([a-z0-9]{4,8})/i,
    /短信验证码[:：]?\s*([a-z0-9]{4,8})/i,
    /登[录陆]验证码为[:：]?\s*([a-z0-9]{4,8})/i,
    /您的验证码为\s*([a-z0-9]{4,8})/i,
    /统一身份认证平台的短信验证码[:：]?\s*([a-z0-9]{4,8})/i,
    /您的动态码为[:：]?\s*([a-z0-9]{4,8})/i,
  ]

  for (const pattern of commonPatterns) {
    const match = content.match(pattern)
    if (match && match[1]) {
      return match[1]
    }
  }

  // 第二种情况：查找内容中的数字序列，通常为6位
  const lines = content.split('\n')
  for (const line of lines) {
    // 如果一行包含"验证码"关键词
    if (line.includes('验证码') || line.includes('动态码') || line.includes('code')) {
      // 尝试提取该行中的连续数字或字母数字组合
      const digitMatch = line.match(/(\d{4,8})(?=[，。,.\s]|$)/)
      if (digitMatch && digitMatch[1]) {
        return digitMatch[1]
      }

      // 尝试提取字母数字混合的验证码
      const alphaNumMatch = line.match(/([a-z0-9]{4,8})(?=[，。,.\s]|$)/i)
      if (alphaNumMatch && alphaNumMatch[1]) {
        return alphaNumMatch[1]
      }
    }
  }

  // 第三种情况：尝试从第二行提取验证码（许多短信的第二行包含验证码）
  if (lines.length >= 2) {
    const secondLine = lines[1]

    // 尝试提取第二行中的数字验证码
    const digitMatch = secondLine.match(/(\d{4,8})(?=[，。,.\s]|$)/)
    if (digitMatch && digitMatch[1]) {
      return digitMatch[1]
    }

    // 尝试提取第二行中的字母数字混合验证码
    const alphaNumMatch = secondLine.match(/([a-z0-9]{4,8})(?=[，。,.\s]|$)/i)
    if (alphaNumMatch && alphaNumMatch[1]) {
      return alphaNumMatch[1]
    }
  }

  // 第四种情况：特殊格式的验证码（例如云堡垒系统的4位字母数字验证码）
  const specialPatterns = [
    /验证码为([a-z0-9]{4})，/i,
    /验证码为([a-z0-9]{4})$/i,
    /验证码为([a-z0-9]{4})[，。,.]?/i,
  ]

  for (const pattern of specialPatterns) {
    const match = content.match(pattern)
    if (match && match[1]) {
      return match[1]
    }
  }

  return null
}

/**
 * 格式化消息内容，高亮显示验证码
 * @param {string} content 短信内容
 * @returns {string} 格式化后的HTML内容
 */
function formatMessageContent(content) {
  if (!content)
    return ''

  const code = extractVerificationCode(content)
  if (!code)
    return content

  // 安全地转义内容，避免XSS
  const escapedContent = content
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;')

  // 用高亮样式替换验证码，为每个字符添加单独的波浪动画
  return escapedContent.replace(
    new RegExp(`(${code})`, 'g'),
    (match) => {
      const chars = match.split('')
      const wrappedChars = chars.map((char, i) =>
        `<span class="char-${i % 4}" style="display:inline-block;">${char}</span>`,
      ).join('')
      return `<span class="verification-code">${wrappedChars}</span>`
    },
  )
}

/**
 * 复制验证码到剪贴板
 * @param {string} content 短信内容
 * @param {number|string} index 消息索引
 */
function copyVerificationCode(content, index) {
  const code = extractVerificationCode(content)
  if (!code)
    return

  navigator.clipboard.writeText(code)
    .then(() => {
      window.$message.success('验证码已复制')

      // 设置该条消息为已复制状态
      copiedStates.value[index] = true

      // 1.5秒后恢复状态
      setTimeout(() => {
        copiedStates.value[index] = false
      }, 1500)
    })
    .catch(() => {
      window.$message.error('复制失败')
    })
}

onMounted(() => {
  async function fetchMessages() {
    try {
      const response = await api.getMsg()
      // 检查响应数据是否有效
      if (!response || !response.data || !Array.isArray(response.data)) {
        window.$message.error('获取短信数据格式异常')
        return
      }

      // 临时存储消息
      const tempMessages = []
      response.data.forEach((msg) => {
        // 确保msg对象有content属性
        if (!msg || typeof msg.content !== 'string') {
          return
        }
        msg.animated = false
        msg.timestamp = Date.now()
        tempMessages.push(msg)
      })

      // 按时间顺序排序（API返回的可能已经排序，但为保险起见）
      messages.value = tempMessages

      // 如果没有短信数据，显示提示
      if (messages.value.length === 0) {
        window.$message.info('暂无短信数据')
      }
    }
    catch (error) {
      console.error('加载短信数据失败:', error)
      window.$message.error(`加载失败: ${error.message || '未知错误'}`)
    }
  }

  fetchMessages()

  // 设置WebSocket连接
  try {
    // 如果是生产环境，直接连接
    if (import.meta.env.PROD) {
      wsManager = new WebSocketManager('/websocket/sms' + '?group=PUBLIC_NOTIFY')
    }
    else {
      wsManager = new WebSocketManager('ws://localhost:18080/websocket/sms' + '?group=PUBLIC_NOTIFY')
    }
    wsManager.connect()

    wsManager.handleMessage = (message) => {
      if (!message)
        return

      if (message.type === 'sms') {
        window.$message.success('收到验证码')
        // 确保消息内容有效
        if (!message.content) {
          console.warn('收到无效的短信内容')
          return
        }

        const sms = {
          type: 'sms',
          content: message.content,
          animated: true,
          timestamp: Date.now(),
        }
        messages.value.unshift(sms)

        // 自动滚动到顶部
        setTimeout(() => {
          const messagesElement = document.getElementById('messages')
          if (messagesElement) {
            messagesElement.scrollTop = 0
          }
        }, 100)
      }
    }
  }
  catch (error) {
    console.error('WebSocket连接失败:', error)
    window.$message.error(`WebSocket连接失败: ${error.message || '未知错误'}`)
  }
})

onUnmounted(() => {
  if (wsManager) {
    wsManager.disconnect()
  }
})
</script>

<style scoped>
/* 海洋背景 */
.ocean-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(to bottom, #e0f7fa, #4fc3f7, #0288d1, #01579b);
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 波浪背景 */
.water-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.3) 0%, transparent 40%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.2) 0%, transparent 30%),
    radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.2) 0%, transparent 40%),
    radial-gradient(circle at 60% 60%, rgba(3, 169, 244, 0.3) 0%, transparent 50%);
  opacity: 0.8;
  z-index: 1;
}

/* 海洋生物 */
.sea-life {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* 小鱼 */
.fish {
  position: absolute;
  width: 40px;
  height: 20px;
  z-index: 3;
}

.fish-1 {
  top: 30%;
  left: 15%;
  animation: swim-right 25s ease-in-out infinite;
}

.fish-2 {
  top: 60%;
  right: 10%;
  animation: swim-left 30s ease-in-out infinite;
  transform: scaleX(-1);
}

.fish-body {
  position: absolute;
  width: 30px;
  height: 15px;
  background: linear-gradient(to right, #29b6f6, #4fc3f7);
  border-radius: 50% 70% 70% 50%;
}

.fish-tail {
  position: absolute;
  right: -10px;
  top: 0;
  width: 15px;
  height: 15px;
  background: #29b6f6;
  clip-path: polygon(0 0, 0 100%, 100% 50%);
  animation: tail-wiggle 0.6s ease-in-out infinite;
  transform-origin: left;
}

@keyframes tail-wiggle {
  0%,
  100% {
    transform: rotate(-5deg);
  }
  50% {
    transform: rotate(5deg);
  }
}

@keyframes swim-right {
  0%,
  100% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(20vw, 10vh);
  }
  50% {
    transform: translate(70vw, -5vh);
  }
  75% {
    transform: translate(40vw, 8vh);
  }
}

@keyframes swim-left {
  0%,
  100% {
    transform: scaleX(-1) translate(0, 0);
  }
  25% {
    transform: scaleX(-1) translate(-30vw, 5vh);
  }
  50% {
    transform: scaleX(-1) translate(-60vw, -10vh);
  }
  75% {
    transform: scaleX(-1) translate(-20vw, 15vh);
  }
}

/* 水母 */
.jellyfish {
  position: absolute;
  top: 40%;
  right: 25%;
  width: 30px;
  height: 50px;
  animation: jellyfish-float 18s ease-in-out infinite;
  z-index: 3;
}

.jellyfish-body {
  position: absolute;
  width: 30px;
  height: 30px;
  background: rgba(144, 202, 249, 0.7);
  border-radius: 50% 50% 30% 30%;
  animation: jellyfish-pulse 2s ease-in-out infinite;
}

.jellyfish-tentacles {
  position: absolute;
  top: 25px;
  left: 0;
  width: 30px;
  height: 25px;
  display: flex;
  justify-content: space-around;
}

.tentacle {
  width: 2px;
  height: 100%;
  background: rgba(144, 202, 249, 0.5);
  animation: tentacle-wiggle 3s ease-in-out infinite;
  transform-origin: top;
}

.tentacle-1 {
  animation-delay: 0s;
}

.tentacle-2 {
  animation-delay: 0.5s;
}

.tentacle-3 {
  animation-delay: 1s;
}

.tentacle-4 {
  animation-delay: 1.5s;
}

@keyframes jellyfish-pulse {
  0%,
  100% {
    transform: scaleY(1);
  }
  50% {
    transform: scaleY(0.8);
  }
}

@keyframes tentacle-wiggle {
  0%,
  100% {
    transform: rotate(-5deg);
  }
  50% {
    transform: rotate(5deg);
  }
}

@keyframes jellyfish-float {
  0%,
  100% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(-10vw, -10vh);
  }
  50% {
    transform: translate(-5vw, 5vh);
  }
  75% {
    transform: translate(5vw, -5vh);
  }
}

/* 水草 */
.seaweed-container {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100px;
  display: flex;
  justify-content: space-between;
  padding: 0 10%;
  z-index: 2;
}

.seaweed {
  width: 8px;
  background: linear-gradient(to top, #26a69a, #4db6ac, #80cbc4);
  border-radius: 5px;
  transform-origin: bottom;
}

.seaweed-1 {
  height: 70px;
  animation: seaweed-sway 5s ease-in-out infinite;
}

.seaweed-2 {
  height: 100px;
  animation: seaweed-sway 6s ease-in-out infinite;
  animation-delay: 1s;
}

.seaweed-3 {
  height: 85px;
  animation: seaweed-sway 4s ease-in-out infinite;
  animation-delay: 0.5s;
}

@keyframes seaweed-sway {
  0%,
  100% {
    transform: rotate(-8deg);
  }
  50% {
    transform: rotate(8deg);
  }
}

/* 波浪动画效果 */
.wave {
  position: absolute;
  width: 200%;
  height: 200px;
  border-radius: 48%;
  background: rgba(255, 255, 255, 0.2);
  animation: wave 15s linear infinite;
}

.wave1 {
  bottom: 10%;
  opacity: 0.2;
  animation-duration: 15s;
}

.wave2 {
  bottom: 15%;
  opacity: 0.3;
  animation-duration: 17s;
  animation-delay: 1s;
}

.wave3 {
  bottom: 20%;
  opacity: 0.1;
  animation-duration: 20s;
  animation-delay: 2s;
}

.wave4 {
  bottom: 25%;
  opacity: 0.2;
  animation-duration: 22s;
  animation-delay: 3s;
}

@keyframes wave {
  0% {
    transform: translate(-50%, 0) rotate(0deg);
  }
  100% {
    transform: translate(-50%, 0) rotate(360deg);
  }
}

/* 漂浮气泡 */
.floating-bubbles {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.bubble-float {
  position: absolute;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.1) 60%,
    transparent 70%
  );
  border-radius: 50%;
  animation: float-up 15s ease-in-out infinite;
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
  z-index: 2;
}

.bubble-float-1 {
  width: 50px;
  height: 50px;
  left: 10%;
  bottom: -50px;
  animation-duration: 15s;
  animation-delay: 0s;
}

.bubble-float-2 {
  width: 30px;
  height: 30px;
  left: 20%;
  bottom: -30px;
  animation-duration: 18s;
  animation-delay: 1s;
}

.bubble-float-3 {
  width: 25px;
  height: 25px;
  left: 35%;
  bottom: -25px;
  animation-duration: 12s;
  animation-delay: 2s;
}

.bubble-float-4 {
  width: 40px;
  height: 40px;
  left: 50%;
  bottom: -40px;
  animation-duration: 16s;
  animation-delay: 3s;
}

.bubble-float-5 {
  width: 35px;
  height: 35px;
  left: 65%;
  bottom: -35px;
  animation-duration: 14s;
  animation-delay: 4s;
}

.bubble-float-6 {
  width: 20px;
  height: 20px;
  left: 80%;
  bottom: -20px;
  animation-duration: 20s;
  animation-delay: 5s;
}

.bubble-float-7 {
  width: 45px;
  height: 45px;
  left: 90%;
  bottom: -45px;
  animation-duration: 17s;
  animation-delay: 6s;
}

.bubble-float-8 {
  width: 25px;
  height: 25px;
  left: 5%;
  bottom: -25px;
  animation-duration: 19s;
  animation-delay: 7s;
}

@keyframes float-up {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-50vh) rotate(180deg);
    opacity: 0.8;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

.elegant-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  background: linear-gradient(135deg, rgba(224, 247, 250, 0.8) 0%, rgba(128, 222, 234, 0.8) 100%);
  border-radius: 15px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  height: 80vh;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 10;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.7);
}

/* 水波纹效果 */
.water-ripple {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  background: radial-gradient(circle at 30% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 20%),
    radial-gradient(circle at 70% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 15%),
    radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.15) 0%, transparent 30%);
  z-index: 1;
}

.header {
  background: linear-gradient(to right, #00acc1 0%, #0288d1 100%);
  color: white;
  padding: 20px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
  overflow: hidden;
}

/* 水波浪动画 */
.water-wave {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 10px;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="rgba(255,255,255,0.2)" d="M0,192L48,197.3C96,203,192,213,288,229.3C384,245,480,267,576,250.7C672,235,768,181,864,181.3C960,181,1056,235,1152,234.7C1248,235,1344,181,1392,154.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  background-repeat: repeat-x;
  background-size: 100% 100%;
  animation: wave-animation 20s linear infinite;
}

@keyframes wave-animation {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 100% 0;
  }
}

.title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  letter-spacing: 1px;
  position: relative;
  display: inline-block;
}

/* 气泡效果 */
.bubble {
  position: absolute;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 8px;
  height: 8px;
  animation: bubble-float 5s infinite ease-in-out;
}

.bubble:nth-child(1) {
  right: -15px;
  top: 5px;
  width: 10px;
  height: 10px;
  animation-delay: 0s;
}

.bubble:nth-child(2) {
  right: -25px;
  top: -5px;
  width: 6px;
  height: 6px;
  animation-delay: 1s;
}

.bubble:nth-child(3) {
  right: -5px;
  top: 15px;
  width: 4px;
  height: 4px;
  animation-delay: 2s;
}

@keyframes bubble-float {
  0%,
  100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

/* 雨滴装饰 */
.raindrop {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  filter: blur(2px);
  transform-origin: center;
  z-index: 1;
  pointer-events: none;
}

.raindrop::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  box-shadow: 0 0 10px 5px rgba(255, 255, 255, 0.3);
  animation: ripple 3s infinite ease-out;
}

.raindrop-1 {
  width: 40px;
  height: 40px;
  top: 15%;
  right: 20%;
  animation: raindrop-1 15s infinite linear;
}

.raindrop-2 {
  width: 30px;
  height: 30px;
  top: 40%;
  left: 15%;
  animation: raindrop-2 12s infinite linear;
}

.raindrop-3 {
  width: 25px;
  height: 25px;
  bottom: 25%;
  right: 10%;
  animation: raindrop-3 10s infinite linear;
}

@keyframes raindrop-1 {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.2;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.4;
  }
}

@keyframes raindrop-2 {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(0.8);
    opacity: 0.2;
  }
}

@keyframes raindrop-3 {
  0%,
  100% {
    transform: scale(0.8);
    opacity: 0.2;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
}

@keyframes ripple {
  0% {
    opacity: 0.5;
    transform: scale(0.8);
  }
  50% {
    opacity: 0;
    transform: scale(1.5);
  }
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
}

.message-scroll-area {
  flex-grow: 1;
  overflow-y: auto;
  padding: 20px;
  scrollbar-width: thin;
  scrollbar-color: #80deea #e0f7fa;
  position: relative;
  z-index: 2;
  background-image: linear-gradient(120deg, rgba(255, 255, 255, 0.1) 0%, transparent 40%),
    linear-gradient(240deg, rgba(255, 255, 255, 0.1) 0%, transparent 40%);
}

.message-scroll-area::-webkit-scrollbar {
  width: 8px;
}

.message-scroll-area::-webkit-scrollbar-track {
  background: #e0f7fa;
  border-radius: 10px;
}

.message-scroll-area::-webkit-scrollbar-thumb {
  background-color: #80deea;
  border-radius: 10px;
}

.card-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.message-card {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 10px;
  padding: 15px;
  box-shadow: 0 4px 8px rgba(0, 188, 212, 0.1);
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
  position: relative;
  border-left: 4px solid #00acc1;
  backdrop-filter: blur(5px);
}

.message-card:hover {
  transform: scale(1.02);
  box-shadow: 0 6px 12px rgba(0, 188, 212, 0.2);
}

.message-card::before {
  content: '';
  position: absolute;
  top: 5px;
  right: 5px;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(0, 172, 193, 0.2), rgba(0, 172, 193, 0.05));
}

.message-content {
  font-size: 0.9rem;
  color: #00838f;
  font-family: 'Courier New', monospace;
}

pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: 'Courier New', monospace;
}

/* 验证码高亮样式 */
:deep(.verification-code) {
  background: linear-gradient(120deg, rgba(0, 172, 193, 0.15) 0%, rgba(0, 131, 143, 0.2) 100%);
  color: #006064;
  font-weight: bold;
  border-radius: 4px;
  padding: 2px 4px;
  margin: 0 2px;
  border-bottom: 2px solid #00acc1;
  position: relative;
  display: inline-block;
  box-shadow: 0 2px 10px rgba(0, 172, 193, 0.2);
  transition: all 0.3s ease;
}

:deep(.verification-code):hover {
  background: linear-gradient(120deg, rgba(0, 172, 193, 0.25) 0%, rgba(0, 131, 143, 0.3) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 172, 193, 0.3);
}

:deep(.verification-code)::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, #00acc1, #0288d1, #00acc1);
  animation: code-highlight 2s infinite linear;
  background-size: 200% 100%;
}

/* 字符动画 */
:deep(.verification-code .char-0) {
  animation: float-char 2s ease-in-out infinite;
  animation-delay: 0s;
}

:deep(.verification-code .char-1) {
  animation: float-char 2s ease-in-out infinite;
  animation-delay: 0.5s;
}

:deep(.verification-code .char-2) {
  animation: float-char 2s ease-in-out infinite;
  animation-delay: 1s;
}

:deep(.verification-code .char-3) {
  animation: float-char 2s ease-in-out infinite;
  animation-delay: 1.5s;
}

@keyframes float-char {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

@keyframes code-highlight {
  0% {
    background-position: 100% 0;
  }
  100% {
    background-position: -100% 0;
  }
}

/* 复制按钮样式 */
.copy-button {
  position: absolute;
  bottom: 10px;
  right: 10px;
  display: flex;
  align-items: center;
  gap: 5px;
  background: linear-gradient(to right, #00acc1, #0288d1);
  color: white;
  border: none;
  border-radius: 5px;
  padding: 5px 10px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.copy-button::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: rgba(255, 255, 255, 0.1);
  transform: rotate(45deg);
  transition: all 0.5s ease;
  opacity: 0;
}

.copy-button:hover::before {
  opacity: 1;
  transform: rotate(45deg) translateY(-10%);
}

.copy-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 188, 212, 0.3);
  background: linear-gradient(to right, #00bcd4, #03a9f4);
}

.copy-button:active {
  transform: translateY(0);
}

.copy-button.copied {
  background: linear-gradient(to right, #26c6da, #29b6f6);
}

.copy-icon {
  display: flex;
  align-items: center;
}

/* 水滴动画效果 */
@keyframes droplet-fall {
  0% {
    opacity: 0;
    transform: translateY(-50px) scale(0.5);
  }
  50% {
    opacity: 0.8;
    transform: translateY(10px) scale(1.1);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.droplet-animation {
  animation: droplet-fall 0.6s cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
  transform-origin: top center;
}

/* 添加光线效果 */
.ocean-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 30%;
  width: 40%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(15deg);
  z-index: 1;
  pointer-events: none;
}

/* 生成更多小气泡 */
@keyframes micro-bubbles {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    transform: translateY(-100px) scale(0.5);
    opacity: 0;
  }
}

.elegant-container::after {
  content: '';
  position: absolute;
  bottom: -20px;
  right: 20px;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: white;
  box-shadow:
    15px -40px 0 1px rgba(255, 255, 255, 0.6),
    30px -80px 0 0px rgba(255, 255, 255, 0.5),
    45px -30px 0 2px rgba(255, 255, 255, 0.7),
    60px -60px 0 1px rgba(255, 255, 255, 0.4),
    75px -20px 0 1px rgba(255, 255, 255, 0.6),
    90px -50px 0 1px rgba(255, 255, 255, 0.5);
  animation: micro-bubbles 15s linear infinite;
  z-index: 1;
}

.elegant-container::before {
  content: '';
  position: absolute;
  bottom: -20px;
  left: 20px;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: white;
  box-shadow:
    15px -30px 0 1px rgba(255, 255, 255, 0.5),
    30px -70px 0 0px rgba(255, 255, 255, 0.4),
    45px -40px 0 2px rgba(255, 255, 255, 0.6),
    60px -20px 0 1px rgba(255, 255, 255, 0.5),
    75px -50px 0 1px rgba(255, 255, 255, 0.4),
    90px -10px 0 0px rgba(255, 255, 255, 0.5);
  animation: micro-bubbles 12s linear infinite;
  animation-delay: 2s;
  z-index: 1;
}
</style>
