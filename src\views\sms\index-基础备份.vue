<template>
  <div class="elegant-container">
    <div class="header">
      <h1 class="title">
        短信验证码
      </h1>
    </div>
    <div id="messages" class="message-scroll-area">
      <div class="card-container">
        <div
          v-for="(message, index) in messages"
          :key="message.timestamp || index"
          :class="{ 'droplet-animation': message.animated }"
          class="message-card"
          @animationend="message.animated = false"
        >
          <div class="message-content">
            <pre>{{ message.content }}</pre>
          </div>
        </div>
      </div>
    </div>
    <TheFooter class="py-12" />
  </div>
</template>

<script setup>
import { TheFooter } from '@/components/index.js'
import api from '@/views/sms/sms.js'
import { onMounted, onUnmounted, ref } from 'vue'
import { WebSocketManager } from './WebSocketManager'

let wsManager

const messages = ref([])

onMounted(() => {
  async function fetchMessages() {
    try {
      const response = await api.getMsg()
      response.data.forEach((msg) => {
        msg.animated = false
        msg.timestamp = Date.now()
        messages.value.push(msg)
      })
    }
    catch (error) {
      $message.error('加载失败', error.value)
    }
    finally { /* empty */
    }
  }

  fetchMessages()
  // 如果是生产环境，直接连接
  if (import.meta.env.PROD) {
    wsManager = new WebSocketManager('/websocket/sms')
  }
  else {
    wsManager = new WebSocketManager('ws://localhost:18080/websocket/sms')
  }
  wsManager.connect()

  wsManager.handleMessage = (message) => {
    if (message.type === 'sms') {
      $message.success('收到验证码')
      const sms = {
        type: 'sms',
        content: message.content,
        animated: true,
        timestamp: Date.now(),
      }
      messages.value.unshift(sms)
    }
  }
})

onUnmounted(() => {
  if (wsManager) {
    wsManager.disconnect()
  }
})
</script>

<style scoped>
.elegant-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 15px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 80vh;
  display: flex;
  flex-direction: column;
}

.header {
  background: linear-gradient(to right, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  letter-spacing: 1px;
}

.message-scroll-area {
  flex-grow: 1;
  overflow-y: auto;
  padding: 20px;
  scrollbar-width: thin;
  scrollbar-color: #9ca3af #e5e7eb;
}

.message-scroll-area::-webkit-scrollbar {
  width: 8px;
}

.message-scroll-area::-webkit-scrollbar-track {
  background: #e5e7eb;
  border-radius: 10px;
}

.message-scroll-area::-webkit-scrollbar-thumb {
  background-color: #9ca3af;
  border-radius: 10px;
}

.card-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.message-card {
  background: white;
  border-radius: 10px;
  padding: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.message-card:hover {
  transform: scale(1.02);
}

.message-content {
  font-size: 0.9rem;
  color: #2c3e50;
  font-family: 'Courier New', monospace;
}

pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: 'Courier New', monospace;
}

/* 水滴动画效果 */
@keyframes droplet-fall {
  0% {
    opacity: 0;
    transform: translateY(-50px) scale(0.5);
  }
  50% {
    opacity: 0.8;
    transform: translateY(10px) scale(1.1);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.droplet-animation {
  animation: droplet-fall 0.6s cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
  transform-origin: top center;
}
</style>
