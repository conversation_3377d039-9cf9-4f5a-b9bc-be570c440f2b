/**********************************
 * @FilePath: index.js
 * @Author: <PERSON>
 * @LastEditor: <PERSON>
 * @LastEditTime: 2023/12/04 22:46:28
 * @Email: <EMAIL>
 * Copyright © 2023 Ronnie <PERSON>(大脸怪) | https://isme.top
 **********************************/

import axios from 'axios'
import { setupInterceptors } from './interceptors'

export function createAxios(options = {}) {
  const defaultOptions = {
    baseURL: import.meta.env.VITE_AXIOS_BASE_URL,
    timeout: 30000,
  }
  const service = axios.create({
    ...defaultOptions,
    ...options,
  })
  setupInterceptors(service)
  return service
}

export const request = createAxios()

/**
 * 上传文件
 * @param url 上传地址
 * @param file 文件
 * @param params 参数
 * @param headers 头信息
 * @returns {*} Promise
 */
export function uploadFile(url, file, params = {}, headers = {}) {
  const formData = new FormData()
  formData.append('file', file)

  // 添加额外的参数到FormData
  for (const key in params) {
    formData.append(key, params[key])
  }

  return request.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
      ...headers,
    },
  })
}
