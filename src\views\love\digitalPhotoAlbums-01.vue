<template>
  <div class="gallery-container">
    <div
      id="box"
      ref="boxRef"
      :style="boxStyle"
      @mousedown.prevent="handleMouseDown"
    >
      <div
        v-for="(item, index) in images"
        :key="item.id"
        :style="getDivStyle(index, item)"
        :class="{ 'animate-ready': animationReady }"
      />
      <p />
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, ref } from 'vue'
import loveApi from './api.js'

const boxRef = ref(null)
const images = ref([])
const perspective = ref(1000)
const animationReady = ref(false)
const rotating = ref(false)
const rotation = ref({ x: -10, y: 0 }) // 初始旋转角度

// 鼠标灵敏度参数调整
const SENSITIVITY = 0.1 // 从0.2降低到0.1
const INERTIA_STRENGTH = 8 // 从15降低到8
const FRICTION = 0.8 // 保持不变

// 计算box的transform样式
const boxStyle = computed(() => ({
  transform: `rotateX(${rotation.value.x}deg) rotateY(${rotation.value.y}deg)`,
}))

let inertiaTimer = null
let lastMousePosition = { x: 0, y: 0 }
let velocity = { x: 0, y: 0 }
let lastTime = 0

// 获取图片数据
async function fetchImages() {
  try {
    const res = await loveApi.pageList({ pageNumber: 1, pageSize: 10 })
    if (res.code === 200) {
      images.value = res.data.records
      setTimeout(() => {
        animationReady.value = true
      }, 100)
    }
  }
  catch (error) {
    console.error('Failed to fetch images:', error)
  }
}

// 计算每个div的初始样式
function getDivStyle(index, item) {
  return {
    background: `url(${item.url}) center/cover`,
    transform: animationReady.value
      ? `rotateY(${index * 36}deg) translate3d(0,0,350px)`
      : 'rotateY(0deg) translate3d(0,0,0)',
  }
}

// 鼠标事件处理
function handleMouseDown(e) {
  if (inertiaTimer) {
    clearInterval(inertiaTimer)
    inertiaTimer = null
  }

  rotating.value = true
  lastMousePosition = { x: e.clientX, y: e.clientY }
  lastTime = Date.now()
  velocity = { x: 0, y: 0 }

  const moveHandler = (e) => {
    if (!rotating.value)
      return

    const currentTime = Date.now()
    const deltaTime = currentTime - lastTime
    const deltaX = e.clientX - lastMousePosition.x
    const deltaY = e.clientY - lastMousePosition.y

    // 计算速度（像素/毫秒）
    velocity.x = deltaX / deltaTime
    velocity.y = deltaY / deltaTime

    // 使用新的灵敏度参数
    rotation.value.y += deltaX * SENSITIVITY
    rotation.value.x -= deltaY * SENSITIVITY

    lastMousePosition = { x: e.clientX, y: e.clientY }
    lastTime = currentTime
  }

  const upHandler = () => {
    rotating.value = false
    document.removeEventListener('mousemove', moveHandler)
    document.removeEventListener('mouseup', upHandler)

    // 添加惯性效果，使用新的惯性强度参数
    inertiaTimer = setInterval(() => {
      velocity.x *= FRICTION
      velocity.y *= FRICTION

      rotation.value.y += velocity.x * INERTIA_STRENGTH
      rotation.value.x -= velocity.y * INERTIA_STRENGTH

      // 当速度足够小时停止动画
      if (Math.abs(velocity.x) < 0.01 && Math.abs(velocity.y) < 0.01) {
        clearInterval(inertiaTimer)
        inertiaTimer = null
      }
    }, 16)
  }

  document.addEventListener('mousemove', moveHandler)
  document.addEventListener('mouseup', upHandler)
}

// 滚轮事件处理
let index = 0
function handleWheel(e) {
  e.preventDefault()
  const d = e.wheelDelta / 120 || -e.detail / 3

  if (d > 0) {
    index -= 20
  }
  else {
    index += 30
  }

  index = Math.max(-1050, index)
  perspective.value = 1000 + index
}

onMounted(async () => {
  await fetchImages()

  document.addEventListener('wheel', handleWheel, { passive: false })

  if (document.onmousewheel !== null) {
    document.addEventListener('DOMMouseScroll', handleWheel, { passive: false })
  }
})

onUnmounted(() => {
  if (inertiaTimer) {
    clearInterval(inertiaTimer)
  }
  document.removeEventListener('wheel', handleWheel)
  document.removeEventListener('DOMMouseScroll', handleWheel)
})
</script>

<style scoped>
.gallery-container {
  height: 100%;
  overflow: hidden;
  display: flex;
  background: #000;
  perspective: v-bind('`${perspective}px`');
  transform-style: preserve-3d;
}

#box {
  position: relative;
  display: flex;
  width: 130px;
  height: 200px;
  margin: auto;
  transform-style: preserve-3d;
  will-change: transform;
}

#box > div {
  transform-style: preserve-3d;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  line-height: 200px;
  font-size: 50px;
  text-align: center;
  box-shadow: 0 0 10px #fff;
  -webkit-box-reflect: below 10px -webkit-linear-gradient(top, rgba(0, 0, 0, 0) 40%, rgba(0, 0, 0, 0.8) 100%);
  will-change: transform;
}

/* 添加初始动画效果 */
#box > div {
  transition: none;
}

#box > div.animate-ready {
  transition: transform 1s;
}

#box > div.animate-ready:nth-child(1) {
  transition-delay: 2s;
}
#box > div.animate-ready:nth-child(2) {
  transition-delay: 1.8s;
}
#box > div.animate-ready:nth-child(3) {
  transition-delay: 1.6s;
}
#box > div.animate-ready:nth-child(4) {
  transition-delay: 1.4s;
}
#box > div.animate-ready:nth-child(5) {
  transition-delay: 1.2s;
}
#box > div.animate-ready:nth-child(6) {
  transition-delay: 1s;
}
#box > div.animate-ready:nth-child(7) {
  transition-delay: 0.8s;
}
#box > div.animate-ready:nth-child(8) {
  transition-delay: 0.6s;
}
#box > div.animate-ready:nth-child(9) {
  transition-delay: 0.4s;
}
#box > div.animate-ready:nth-child(10) {
  transition-delay: 0.2s;
}

#box p {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  width: 1200px;
  height: 1200px;
  background: -webkit-radial-gradient(center center, 600px 600px, rgba(50, 50, 50, 1), rgba(0, 0, 0, 0));
  border-radius: 50%;
  transform: rotateX(90deg) translate3d(-600px, 0, -105px);
}
</style>
