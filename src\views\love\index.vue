<template>
  <CommonPage>
    <!-- 上传按钮 -->
    <div class="mb-4 w-full flex justify-end">
      <n-button type="primary" @click="showUploadDialog = true">
        <template #icon>
          <i class="i-mdi:upload mr-1" />
        </template>
        上传图片
      </n-button>
    </div>

    <!-- 图片列表展示 -->
    <n-card v-if="imgList && imgList.length">
      <n-image-group>
        <n-space justify="space-between" align="center">
          <n-card
            v-for="(item, index) in imgList"
            :key="index"
            class="w-280 transition-all duration-300 hover:card-shadow"
          >
            <div class="h-160 f-c-c">
              <n-image width="200" :src="item.thUrl" :preview-src="item.url" />
            </div>
            <div class="mt-4 space-y-2">
              <div class="text-gray-600">
                标题：{{ item.title || '无' }}
              </div>
              <div class="text-gray-600">
                备注：{{ item.remarks || '无' }}
              </div>
              <div class="flex justify-end space-x-2">
                <n-button size="small" type="info" @click="openEditDialog(item)">
                  编辑
                </n-button>
                <n-button size="small" type="error" @click="confirmDelete(item)">
                  删除
                </n-button>
              </div>
            </div>
          </n-card>
          <div v-for="i in 4" :key="i" class="w-280" />
        </n-space>
      </n-image-group>

      <!-- 分页器 -->
      <div class="mt-4 flex justify-end">
        <n-pagination
          v-model:page="currentPage"
          v-model:page-size="pageSize"
          :item-count="totalItems"
          :page-sizes="pageSizes"
          show-size-picker
          :page-slot="pageSlot"
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        >
          <template #prefix="{ itemCount }">
            共 {{ itemCount }} 条数据
          </template>
        </n-pagination>
      </div>
    </n-card>

    <!-- 上传对话框 -->
    <n-modal
      v-model:show="showUploadDialog"
      preset="dialog"
      title="上传图片"
      :mask-closable="false"
    >
      <n-upload
        class="w-full p-8"
        :custom-request="handleUpload"
        :show-file-list="false"
        accept=".png,.jpg,.jpeg"
        @before-upload="onBeforeUpload"
      >
        <n-upload-dragger>
          <div class="h-32 f-c-c flex-col">
            <i class="i-mdi:upload mb-2 text-36 color-primary" />
            <n-text class="text-14 color-gray">
              点击或者拖动文件到该区域来上传
            </n-text>
          </div>
        </n-upload-dragger>
      </n-upload>
    </n-modal>

    <!-- 编辑对话框 -->
    <n-modal
      v-model:show="showEditDialog"
      preset="dialog"
      title="编辑信息"
      :mask-closable="false"
      negative-text="取消"
      positive-text="提交"
      @negative-click="showEditDialog = false"
      @positive-click="handleEditSubmit"
    >
      <n-form ref="editFormRef" :model="editForm" :rules="rules">
        <n-form-item label="标题" path="title">
          <n-input v-model:value="editForm.title" placeholder="请输入标题" />
        </n-form-item>
        <n-form-item label="备注" path="remarks">
          <n-input
            v-model:value="editForm.remarks"
            type="textarea"
            placeholder="请输入备注"
          />
        </n-form-item>
      </n-form>
    </n-modal>
  </CommonPage>
</template>

<script setup>
import api from '@/api/fileDetailApi.js'
import { CommonPage } from '@/components/index.js'
import { useClipboard } from '@vueuse/core'
import { ref } from 'vue'
import loveApi from './api.js'

defineOptions({ name: 'ImgUpload' })

const { copied } = useClipboard()

// 表单校验规则
const rules = {
  title: { required: true, message: '请输入标题', trigger: 'blur' },
}

// 上传相关
const showUploadDialog = ref(false)
const uploadForm = ref({
  title: '',
  remarks: '',
  fileId: null,
})
const formRef = ref(null)

// 编辑相关
const showEditDialog = ref(false)
const editForm = ref({
  id: null,
  title: '',
  remarks: '',
})
const editFormRef = ref(null)
const currentEditItem = ref(null)

// 列表相关
const imgList = ref([])
const totalItems = ref(0)
const currentPage = ref(1)
const pageSize = ref(21)
const pageSizes = ref([21, 42, 84])
const pageSlot = ref(0)

// 打开编辑对话框
function openEditDialog(item) {
  currentEditItem.value = item
  editForm.value = {
    id: item.id,
    title: item.title || '',
    remarks: item.remarks || '',
  }
  showEditDialog.value = true
}

// 提交编辑
async function handleEditSubmit() {
  await editFormRef.value?.validate()
  await loveApi.update(editForm.value)
  $message.success('更新成功')
  showEditDialog.value = false
  pageList()
}

// 确认删除
function confirmDelete(item) {
  $dialog.warning({
    title: '提示',
    content: '确定要删除这张图片吗？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      await loveApi.delete(item.id)
      $message.success('删除成功')
      pageList()
    },
  })
}

// 分页处理
function handlePageChange(page) {
  currentPage.value = page
  pageList()
}

function handlePageSizeChange(size) {
  pageSize.value = size
  currentPage.value = 1
  pageList()
}

// 获取列表数据
function pageList() {
  loveApi.pageList({
    pageNumber: currentPage.value,
    pageSize: pageSize.value,
  }).then((res) => {
    if (res.code === 200) {
      imgList.value = res.data.records
      totalItems.value = res.data.totalRow
    }
  })
}

// 上传前校验
function onBeforeUpload({ file }) {
  if (!file.file?.type.startsWith('image/')) {
    $message.error('只能上传图片文件')
    return false
  }
  return true
}

// 处理上传
async function handleUpload({ file, onFinish }) {
  if (!file || !file.type) {
    $message.error('请选择文件')
    return
  }

  $message.loading('上传中...')
  const response = await api.uploadImage(file.file, {})

  if (response.code === 200) {
    onFinish()
    uploadForm.value.fileId = response.data.id
    $message.success('图片上传成功，请填写信息')
    showUploadDialog.value = false
    await submitImageInfo()
  }
  else {
    $message.error('上传失败')
  }
}

// 提交图片信息
async function submitImageInfo() {
  await formRef.value?.validate()
  await loveApi.create({
    fileId: uploadForm.value.fileId,
    fileType: '1',
    title: uploadForm.value.title,
    remarks: uploadForm.value.remarks,
  })
  pageList()
}

// 监听复制操作
watch(copied, (val) => {
  if (val)
    $message.success('已复制到剪切板')
})

// 初始化加载
onMounted(() => {
  pageList()
})
</script>

<style scoped>
.hover\:card-shadow:hover {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
</style>
